'use client';

import { useEffect } from 'react';
import { useParams } from 'next/navigation';
import { languageConfig, rtlLocales, type Locale } from '@/i18n';

/**
 * HTML属性提供者组件
 * 负责动态设置HTML元素的lang和dir属性
 */
export function HtmlAttributesProvider() {
  const params = useParams();
  const locale = params?.locale as Locale;

  useEffect(() => {
    if (!locale || !languageConfig[locale]) {
      return;
    }

    const config = languageConfig[locale];
    const isRTL = rtlLocales.includes(locale);
    
    // 获取HTML元素
    const htmlElement = document.documentElement;
    
    // 设置语言属性
    htmlElement.setAttribute('lang', locale);
    
    // 设置方向属性
    htmlElement.setAttribute('dir', config.direction);
    
    // 设置字体类
    const getFontClass = (fontFamily: string, locale: string) => {
      switch (fontFamily) {
        case 'chinese':
          return locale === 'zh-TW' ? 'font-noto-sans-tc' : 'font-noto-sans-sc';
        case 'japanese':
          return 'font-noto-sans-jp';
        case 'hindi':
          return 'font-noto-sans';
        default:
          return 'font-inter';
      }
    };

    // 移除之前的字体类
    const fontClasses = ['font-inter', 'font-noto-sans-sc', 'font-noto-sans-tc', 'font-noto-sans-jp', 'font-noto-sans'];
    fontClasses.forEach(cls => htmlElement.classList.remove(cls));
    
    // 添加新的字体类
    const fontClass = getFontClass(config.fontFamily, locale);
    htmlElement.classList.add(fontClass);

    // 设置CSS变量
    htmlElement.style.setProperty('--expansion-factor', config.expansionFactor.toString());
    htmlElement.style.setProperty('--locale-direction', config.direction);
    htmlElement.style.setProperty('--locale-font', fontClass);

  }, [locale]);

  return null; // 这个组件不渲染任何内容
}
