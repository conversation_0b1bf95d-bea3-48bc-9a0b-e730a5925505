# 📊 数据库架构总览

## 🎯 核心设计理念

### 技术选型
- **Cloudflare D1**：基于SQLite的边缘数据库
- **Prisma ORM**：类型安全的数据库操作
- **JSON存储**：利用SQLite原生JSON函数处理复杂数据

### 设计原则
1. **边缘优先**：全球分布式存储，低延迟访问
2. **多语言支持**：通过locale字段和JSON存储
3. **SEO优化**：合理的索引设计和URL结构
4. **性能优先**：优化的查询和缓存策略

## 📋 数据库表结构

### 核心业务表
```
users              # 用户基础信息
├── id, email, username
├── locale, theme
└── 关联: testResults, blogViews, comments, favorites

blog_posts         # 博客文章（多语言SEO优化）
├── title, slug, content, excerpt
├── locale, category, tags (JSON)
├── seoTitle, seoDescription, keywords (JSON)
├── metadata (JSON), status, publishedAt
└── 关联: views, comments, favorites

test_results       # 测试结果（支持分享）
├── userId, testType
├── answers (JSON), result (JSON)
├── shareToken, isPublic
└── 关联: user

comments           # 评论系统（支持嵌套）
├── content, userId, postId
├── parentId (自关联)
├── isApproved
└── 关联: user, post, parent, replies
```

### 辅助功能表
```
user_favorites     # 用户收藏
user_sessions      # 会话管理
user_verifications # 邮箱验证
blog_views         # 浏览统计
```

## 🔧 快速操作指南

### 开发环境
```bash
# 启动开发
npm run dev

# 数据库操作
npm run db:generate        # 生成Prisma客户端
npm run d1:migrate:local   # 本地迁移
wrangler d1 execute tarot-seo-db --local --command="SELECT * FROM users LIMIT 5"
```

### 生产环境
```bash
# 部署流程
npm run d1:migrate:remote  # 生产迁移
npm run deploy             # 部署应用

# 备份恢复
wrangler d1 export tarot-seo-db --output=backup.sql
wrangler d1 execute tarot-seo-db --file=backup.sql
```

### JSON数据查询
```sql
-- 标签搜索
SELECT * FROM blog_posts 
WHERE json_extract(tags, '$') LIKE '%"react"%';

-- AI生成内容
SELECT * FROM blog_posts 
WHERE json_extract(metadata, '$.aiGenerated') = true;
```

## 📚 详细文档

- **完整设置指南**：`docs/cloudflare-d1-setup.md`
- **数据库规范**：`.augment/rules/07-database-api-rules.md`
- **数据模型定义**：`prisma/schema.prisma`
- **环境变量配置**：`docs/ENV_SETUP.md`

## ⚡ 性能优化

### 索引策略
- `blog_posts`: locale+category, status+publishedAt, slug
- `test_results`: userId+testType, shareToken
- `comments`: postId+createdAt

### 缓存策略
- **博客文章**：1小时TTL
- **测试结果**：24小时TTL
- **用户偏好**：6小时TTL

### 限制和配额
- **免费额度**：100K读取/天，50K写入/天
- **查询限制**：免费版50次/调用，付费版1000次/调用
- **连接限制**：最大6个并发连接
