import { z } from 'zod';

// 环境变量验证schema
const envSchema = z.object({
  // 应用配置
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NEXT_PUBLIC_APP_NAME: z.string().default('Mystical Website'),
  
  // 数据库配置 - Cloudflare D1
  DATABASE_URL: z.string().min(1), // 本地开发使用SQLite文件

  // Cloudflare配置
  CLOUDFLARE_API_TOKEN: z.string().optional(),
  CLOUDFLARE_ACCOUNT_ID: z.string().optional(),
  CLOUDFLARE_ZONE_ID: z.string().optional(),
  
  // Redis配置
  REDIS_URL: z.string().optional(),
  UPSTASH_REDIS_REST_URL: z.string().optional(),
  UPSTASH_REDIS_REST_TOKEN: z.string().optional(),
  
  // 认证配置
  NEXTAUTH_SECRET: z.string().min(1),
  NEXTAUTH_URL: z.string().url().optional(),
  
  // AI服务配置
  QWEN_API_KEY: z.string().optional(),
  QWEN_API_URL: z.string().url().optional(),
  DOUBAO_API_KEY: z.string().optional(),
  DOUBAO_API_URL: z.string().url().optional(),
  ZHIPU_API_KEY: z.string().optional(),
  ZHIPU_API_URL: z.string().url().optional(),
  
  // 内容管理
  SANITY_PROJECT_ID: z.string().optional(),
  SANITY_DATASET: z.string().default('production'),
  SANITY_API_TOKEN: z.string().optional(),
  
  // 监控和分析
  SENTRY_DSN: z.string().optional(),
  SENTRY_ORG: z.string().optional(),
  SENTRY_PROJECT: z.string().optional(),
  SENTRY_AUTH_TOKEN: z.string().optional(),
  NEXT_PUBLIC_UMAMI_WEBSITE_ID: z.string().optional(),
  NEXT_PUBLIC_UMAMI_URL: z.string().url().optional(),
  
  // 邮件服务
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.coerce.number().optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  
  // 文件存储 - Cloudflare R2
  CLOUDFLARE_R2_ACCOUNT_ID: z.string().optional(),
  CLOUDFLARE_R2_ACCESS_KEY_ID: z.string().optional(),
  CLOUDFLARE_R2_SECRET_ACCESS_KEY: z.string().optional(),
  CLOUDFLARE_R2_BUCKET_NAME: z.string().optional(),

  // Cloudflare Images
  CLOUDFLARE_IMAGES_ACCOUNT_ID: z.string().optional(),
  CLOUDFLARE_IMAGES_API_TOKEN: z.string().optional(),
  
  // 社交媒体
  NEXT_PUBLIC_FACEBOOK_APP_ID: z.string().optional(),
  NEXT_PUBLIC_TWITTER_HANDLE: z.string().optional(),
  NEXT_PUBLIC_INSTAGRAM_HANDLE: z.string().optional(),
  
  // SEO
  NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION: z.string().optional(),
  NEXT_PUBLIC_BING_SITE_VERIFICATION: z.string().optional(),
  
  // 开发工具
  ANALYZE: z.coerce.boolean().default(false),
  SKIP_ENV_VALIDATION: z.coerce.boolean().default(false),
});

// 验证环境变量
function validateEnv() {
  if (process.env['SKIP_ENV_VALIDATION'] === 'true') {
    console.warn('⚠️ Skipping environment validation');
    return process.env;
  }

  try {
    const env = envSchema.parse(process.env);
    console.log('✅ Environment variables validated successfully');
    return env;
  } catch (error) {
    console.error('❌ Environment validation failed:');
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`);
      });
    }
    process.exit(1);
  }
}

// 导出验证后的环境变量
export const env = validateEnv();

// 类型定义
export type Env = z.infer<typeof envSchema>;

// 环境检查工具函数
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';

// 功能可用性检查
export const features = {
  redis: !!(env.REDIS_URL || (env.UPSTASH_REDIS_REST_URL && env.UPSTASH_REDIS_REST_TOKEN)),
  sentry: !!env.SENTRY_DSN,
  analytics: !!env.NEXT_PUBLIC_UMAMI_WEBSITE_ID,
  email: !!(env.SMTP_HOST && env.SMTP_USER && env.SMTP_PASS),
  cloudflareR2: !!(env.CLOUDFLARE_R2_ACCOUNT_ID && env.CLOUDFLARE_R2_ACCESS_KEY_ID),
  cloudflareImages: !!(env.CLOUDFLARE_IMAGES_ACCOUNT_ID && env.CLOUDFLARE_IMAGES_API_TOKEN),
  ai: {
    qwen: !!env.QWEN_API_KEY,
    doubao: !!env.DOUBAO_API_KEY,
    zhipu: !!env.ZHIPU_API_KEY,
  },
  cms: !!(env.SANITY_PROJECT_ID && env.SANITY_API_TOKEN),
} as const;

// 配置对象
export const config = {
  app: {
    name: env.NEXT_PUBLIC_APP_NAME,
    url: env.NEXT_PUBLIC_APP_URL,
    env: env.NODE_ENV,
  },
  database: {
    url: env.DATABASE_URL, // 本地开发使用SQLite，生产环境使用D1
  },
  cloudflare: {
    apiToken: env.CLOUDFLARE_API_TOKEN,
    accountId: env.CLOUDFLARE_ACCOUNT_ID,
    zoneId: env.CLOUDFLARE_ZONE_ID,
  },
  redis: {
    url: env.REDIS_URL,
    upstash: {
      url: env.UPSTASH_REDIS_REST_URL,
      token: env.UPSTASH_REDIS_REST_TOKEN,
    },
  },
  auth: {
    secret: env.NEXTAUTH_SECRET,
    url: env.NEXTAUTH_URL,
  },
  ai: {
    qwen: {
      apiKey: env.QWEN_API_KEY,
      apiUrl: env.QWEN_API_URL,
    },
    doubao: {
      apiKey: env.DOUBAO_API_KEY,
      apiUrl: env.DOUBAO_API_URL,
    },
    zhipu: {
      apiKey: env.ZHIPU_API_KEY,
      apiUrl: env.ZHIPU_API_URL,
    },
  },
  monitoring: {
    sentry: {
      dsn: env.SENTRY_DSN,
      org: env.SENTRY_ORG,
      project: env.SENTRY_PROJECT,
      authToken: env.SENTRY_AUTH_TOKEN,
    },
    umami: {
      websiteId: env.NEXT_PUBLIC_UMAMI_WEBSITE_ID,
      url: env.NEXT_PUBLIC_UMAMI_URL,
    },
  },
  email: {
    host: env.SMTP_HOST,
    port: env.SMTP_PORT,
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
  },
  storage: {
    r2: {
      accountId: env.CLOUDFLARE_R2_ACCOUNT_ID,
      accessKeyId: env.CLOUDFLARE_R2_ACCESS_KEY_ID,
      secretAccessKey: env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
      bucketName: env.CLOUDFLARE_R2_BUCKET_NAME,
    },
    images: {
      accountId: env.CLOUDFLARE_IMAGES_ACCOUNT_ID,
      apiToken: env.CLOUDFLARE_IMAGES_API_TOKEN,
    },
  },
  cms: {
    sanity: {
      projectId: env.SANITY_PROJECT_ID,
      dataset: env.SANITY_DATASET,
      apiToken: env.SANITY_API_TOKEN,
    },
  },
  social: {
    facebook: env.NEXT_PUBLIC_FACEBOOK_APP_ID,
    twitter: env.NEXT_PUBLIC_TWITTER_HANDLE,
    instagram: env.NEXT_PUBLIC_INSTAGRAM_HANDLE,
  },
  seo: {
    googleVerification: env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
    bingVerification: env.NEXT_PUBLIC_BING_SITE_VERIFICATION,
  },
} as const;
