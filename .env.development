# ============================================================================
# 玄学多语言网站 - 开发环境配置
# 基于 Cloudflare 技术栈：D1 + Workers + R2 + KV
# ============================================================================

# 应用基础配置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Mystical Website"

# ============================================================================
# 数据库配置 - Cloudflare D1
# ============================================================================
# 本地开发使用本地SQLite数据库
DATABASE_URL="file:./dev.db"

# Cloudflare D1配置（通过wrangler.toml管理）
# 生产环境通过Cloudflare Workers绑定访问
# D1数据库ID: 87945106-1e0d-4375-967c-d89324987198

# ============================================================================
# 缓存配置 - Upstash Redis + Cloudflare KV
# ============================================================================
# 本地开发Redis（可选）
REDIS_URL="redis://localhost:6379"

# Upstash Redis（免费层：10K请求/天，256MB存储）
UPSTASH_REDIS_REST_URL="your-upstash-redis-url"
UPSTASH_REDIS_REST_TOKEN="your-upstash-redis-token"

# ============================================================================
# 认证配置
# ============================================================================
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"
NEXTAUTH_URL="http://localhost:3000"

# ============================================================================
# AI服务配置 - 多AI提供商策略
# ============================================================================
# 通义千问 (阿里云 - 主要AI服务)
QWEN_API_KEY="your-qwen-api-key"
QWEN_API_URL="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# 豆包 (字节跳动 - 备用AI服务)
DOUBAO_API_KEY="your-doubao-api-key"
DOUBAO_API_URL="https://ark.cn-beijing.volces.com/api/v3/chat/completions"

# 智谱AI (清华 - 多语言支持)
ZHIPU_API_KEY="your-zhipu-api-key"
ZHIPU_API_URL="https://open.bigmodel.cn/api/paas/v4/chat/completions"

# ============================================================================
# 文件存储 - Cloudflare R2
# ============================================================================
CLOUDFLARE_R2_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_R2_ACCESS_KEY_ID="your-r2-access-key"
CLOUDFLARE_R2_SECRET_ACCESS_KEY="your-r2-secret-key"
CLOUDFLARE_R2_BUCKET_NAME="tarot-seo-assets"

# Cloudflare Images（可选，用于图片优化）
CLOUDFLARE_IMAGES_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_IMAGES_API_TOKEN="your-images-api-token"

# ============================================================================
# 监控和分析
# ============================================================================
# Sentry错误监控
SENTRY_DSN="your-sentry-dsn"
SENTRY_ORG="your-sentry-org"
SENTRY_PROJECT="your-sentry-project"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"

# Umami网站分析（开源，隐私友好）
NEXT_PUBLIC_UMAMI_WEBSITE_ID="your-umami-website-id"
NEXT_PUBLIC_UMAMI_URL="https://umami.mystical-website.com"

# ============================================================================
# 邮件服务
# ============================================================================
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# ============================================================================
# 社交媒体集成
# ============================================================================
NEXT_PUBLIC_FACEBOOK_APP_ID="your-facebook-app-id"
NEXT_PUBLIC_TWITTER_HANDLE="@mystical_website"
NEXT_PUBLIC_INSTAGRAM_HANDLE="mystical_website"

# ============================================================================
# SEO配置
# ============================================================================
NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION="your-google-site-verification"
NEXT_PUBLIC_BING_SITE_VERIFICATION="your-bing-site-verification"

# ============================================================================
# Cloudflare部署配置
# ============================================================================
CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"
CLOUDFLARE_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_ZONE_ID="your-cloudflare-zone-id"

# ============================================================================
# 开发工具配置
# ============================================================================
ANALYZE=false
SKIP_ENV_VALIDATION=false
WRANGLER_SEND_METRICS=false

# ============================================================================
# 简化策略说明
# ============================================================================
# 本文件作为项目默认配置，包含非敏感的占位符
# 真实的API密钥和敏感信息请配置在 .env.local 文件中
# 生产环境配置请在部署平台Dashboard中设置

# 环境区分策略：
# - 开发环境：.env.local 文件 + NODE_ENV=development
# - 生产环境：部署平台配置 + NODE_ENV=production
# - 预览环境：部署平台自动处理

# ============================================================================
# 部署配置（在部署平台Dashboard中配置，不在代码中）
# ============================================================================
# VERCEL_TOKEN="your-vercel-token"
# VERCEL_ORG_ID="your-vercel-org-id"
# VERCEL_PROJECT_ID="your-vercel-project-id"
