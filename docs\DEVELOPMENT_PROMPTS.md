# 开发提示词库

## 🎯 基础架构开发提示词

### 项目初始化
```
我要初始化玄学多语言网站项目，请基于以下rules进行开发：
- 主要规则：00-master-rules.md（项目总纲和技术栈）
- 辅助规则：05-deployment-monitoring-rules.md（部署配置）
- 特别注意：
  1. 使用Next.js 14 App Router
  2. 配置TypeScript严格模式
  3. 设置Tailwind CSS神秘主题色彩
  4. 配置Vercel部署和GitHub集成
  5. 设置环境变量管理

请创建完整的项目结构，包括package.json、tsconfig.json、tailwind.config.js等配置文件。
```

### 设计系统实现
```
我要实现设计系统基础组件，请基于以下rules进行开发：
- 主要规则：01-frontend-design-rules.md（完整设计系统）
- 特别注意：
  1. 实现神秘紫色+黄金色配色系统
  2. 配置4种字体族（sans、serif、mystical、mono）
  3. 创建原子组件（Button、Input、Typography等）
  4. 实现深色模式支持
  5. 配置响应式断点系统

请严格按照设计规范创建组件，确保颜色、字体、间距完全一致。
```

## 🌍 多语言开发提示词

### 多语言架构配置
```
我要实现多语言架构，请基于以下rules进行开发：
- 主要规则：06-mobile-multilingual-rules.md（多语言规范）
- 辅助规则：00-master-rules.md（多语言策略）
- 特别注意：
  1. 配置next-intl支持6种语言（en、zh、es、pt、hi、ja）
  2. 实现SEO友好的URL结构：/[locale]/[category]/[slug]
  3. 配置hreflang标签
  4. 实现语言切换组件
  5. 支持RTL语言布局
  6. 实现文化敏感的设计适配

请确保每种语言都有独立的SEO元数据和内容管理。
```

### 语言切换组件
```
我要创建语言切换组件，请基于以下rules进行开发：
- 主要规则：06-mobile-multilingual-rules.md（多语言规范）
- 辅助规则：01-frontend-design-rules.md（设计系统）
- 特别注意：
  1. 支持6种语言切换
  2. 显示语言原生名称
  3. 保持当前页面路径
  4. 移动端友好的下拉菜单
  5. 无障碍访问支持

请创建美观且实用的语言切换器。
```

## 📝 博客系统开发提示词

### 博客文章页面
```
我要开发博客文章页面，请基于以下rules进行开发：
- 主要规则：03-blog-management-rules.md（博客管理系统）
- 辅助规则：01-frontend-design-rules.md（博客页面设计）
- 特别注意：
  1. 实现对标Medium的文章页面设计
  2. 680px最佳阅读宽度，1.75倍行高
  3. 完整的文章头部（标题、作者、封面图）
  4. 优雅的正文排版（段落、标题、列表、引用、代码）
  5. 阅读进度指示器
  6. 目录导航（桌面端浮动，移动端可折叠）
  7. 社交分享和评论功能

请严格按照设计规范实现，确保阅读体验达到Medium水准。
```

### 博客列表页面
```
我要开发博客列表页面，请基于以下rules进行开发：
- 主要规则：03-blog-management-rules.md（博客列表设计）
- 辅助规则：01-frontend-design-rules.md（设计系统）
- 特别注意：
  1. 特色文章大卡片展示
  2. 常规文章网格布局
  3. 分类导航和筛选
  4. 搜索功能
  5. 分页导航
  6. 侧边栏（热门文章、标签云、订阅表单）
  7. 响应式布局适配

请创建专业且用户友好的博客列表页面。
```

### 内容管理后台
```
我要开发内容管理后台，请基于以下rules进行开发：
- 主要规则：03-blog-management-rules.md（内容管理系统）
- 辅助规则：02-component-architecture-rules.md（组件架构）
- 特别注意：
  1. AI内容导入功能
  2. 批量导入处理
  3. 内容质量检查
  4. SEO优化建议
  5. 发布计划管理
  6. 多语言内容管理

请创建高效的内容管理工具。
```

## 🔮 测试功能开发提示词

### 塔罗牌测试系统
```
我要开发塔罗牌测试系统，请基于以下rules进行开发：
- 主要规则：04-online-tests-rules.md（测试功能规范）
- 辅助规则：02-component-architecture-rules.md（塔罗牌组件）
- 特别注意：
  1. 创建塔罗牌卡片组件（翻转动画、选中效果）
  2. 实现塔罗牌展开布局（单张、三张、凯尔特十字）
  3. 卡片选择交互（洗牌动画、选择反馈）
  4. AI解读功能集成
  5. 结果展示和分享
  6. 多语言支持

请创建神秘且专业的塔罗牌测试体验。
```

### 星座测试系统
```
我要开发星座测试系统，请基于以下rules进行开发：
- 主要规则：04-online-tests-rules.md（测试功能规范）
- 辅助规则：02-component-architecture-rules.md（星座组件）
- 特别注意：
  1. 创建星座轮盘组件（12星座布局、元素分组）
  2. 星座选择交互（悬停效果、选中动画）
  3. 星座配对功能
  4. 性格分析测试
  5. AI个性化解读
  6. 运势预测功能

请创建美观且准确的星座测试系统。
```

### 数字命理系统
```
我要开发数字命理系统，请基于以下rules进行开发：
- 主要规则：04-online-tests-rules.md（测试功能规范）
- 辅助规则：02-component-architecture-rules.md（数字命理组件）
- 特别注意：
  1. 生命数字计算器（出生日期+姓名）
  2. 计算步骤可视化
  3. 数字能量轮盘展示
  4. 数字含义详细解释
  5. 个性化分析报告
  6. 多语言数字解读

请创建准确且易懂的数字命理工具。
```

## 📱 移动端开发提示词

### 移动端导航
```
我要开发移动端导航，请基于以下rules进行开发：
- 主要规则：06-mobile-multilingual-rules.md（移动端规范）
- 辅助规则：01-frontend-design-rules.md（设计系统）
- 特别注意：
  1. 汉堡菜单设计
  2. 侧边抽屉导航
  3. 触摸友好的交互
  4. 语言切换集成
  5. 搜索功能
  6. 用户账户入口

请创建直观且高效的移动端导航。
```

### 移动端测试体验
```
我要优化移动端测试体验，请基于以下rules进行开发：
- 主要规则：06-mobile-multilingual-rules.md（移动端规范）
- 辅助规则：04-online-tests-rules.md（测试功能）
- 特别注意：
  1. 触摸友好的卡片选择
  2. 手势操作支持
  3. 移动端专用布局
  4. 性能优化
  5. 离线功能支持
  6. 分享功能优化

请确保移动端测试体验流畅自然。
```

## 👤 用户系统开发提示词

### 用户认证系统
```
我要开发用户认证系统，请基于以下rules进行开发：
- 主要规则：08-user-system-rules.md（用户系统规范）
- 辅助规则：07-database-api-rules.md（数据库和API设计）
- 特别注意：
  1. 实现完整的注册/登录流程
  2. 支持第三方登录（Google、GitHub、Apple）
  3. 邮箱验证和密码重置功能
  4. JWT + Refresh Token认证策略
  5. 安全措施（速率限制、设备跟踪）
  6. 游客模式和注册引导

请创建安全且用户友好的认证系统。
```

### 用户界面组件
```
我要开发用户界面组件，请基于以下rules进行开发：
- 主要规则：08-user-system-rules.md（用户组件规范）
- 辅助规则：01-frontend-design-rules.md（设计系统）
- 特别注意：
  1. 登录/注册表单组件
  2. 用户头像和资料卡片
  3. 用户设置页面
  4. 社交登录按钮
  5. 权限控制组件
  6. 响应式设计适配

请确保组件设计美观且易用。
```

### 评论系统
```
我要开发评论系统，请基于以下rules进行开发：
- 主要规则：08-user-system-rules.md（用户系统）
- 辅助规则：03-blog-management-rules.md（博客评论设计）
- 特别注意：
  1. 评论发表和回复功能
  2. 评论审核机制
  3. 用户权限控制
  4. 评论嵌套显示
  5. 实时更新功能
  6. 反垃圾评论措施

请创建功能完整的评论系统。
```

### 用户权限管理
```
我要实现用户权限管理，请基于以下rules进行开发：
- 主要规则：08-user-system-rules.md（权限系统）
- 辅助规则：07-database-api-rules.md（API安全）
- 特别注意：
  1. 角色和权限定义
  2. 权限检查中间件
  3. 前端权限控制组件
  4. 路由保护机制
  5. API权限验证
  6. 管理员功能

请确保权限系统安全可靠。
```

## 🗄️ 数据库开发提示词

### 数据库设计
```
我要设计数据库架构，请基于以下rules进行开发：
- 主要规则：07-database-api-rules.md（数据库设计）
- 特别注意：
  1. 用户、博客、测试结果表设计
  2. 多语言内容存储策略
  3. 索引优化配置
  4. 数据关系设计
  5. 性能优化考虑
  6. 备份恢复策略

请创建高效且可扩展的数据库架构。
```

### API接口开发
```
我要开发API接口，请基于以下rules进行开发：
- 主要规则：07-database-api-rules.md（API设计规范）
- 特别注意：
  1. RESTful API设计
  2. 统一响应格式
  3. 错误处理机制
  4. 认证授权系统
  5. 速率限制
  6. API文档生成

请创建安全且高效的API接口。
```

## 🚀 部署优化提示词

### 性能优化
```
我要进行性能优化，请基于以下rules进行开发：
- 主要规则：05-deployment-monitoring-rules.md（性能监控）
- 辅助规则：00-master-rules.md（性能原则）
- 特别注意：
  1. Core Web Vitals优化
  2. 图片懒加载和优化
  3. 代码分割策略
  4. 缓存配置
  5. CDN配置
  6. 监控告警设置

请确保网站性能达到优秀水准。
```

### SEO优化
```
我要进行SEO优化，请基于以下rules进行开发：
- 主要规则：00-master-rules.md（SEO至上原则）
- 辅助规则：03-blog-management-rules.md（SEO策略）
- 特别注意：
  1. 动态SEO元数据生成
  2. 结构化数据配置
  3. 站点地图生成
  4. robots.txt配置
  5. 内部链接优化
  6. 页面速度优化

目标是达到DA 60+的权威网站水准。
```

## 🧪 测试开发提示词

### 组件测试
```
我要为组件编写测试，请基于以下rules进行开发：
- 主要规则：02-component-architecture-rules.md（测试规范）
- 特别注意：
  1. 使用Jest + React Testing Library
  2. 90%+代码覆盖率
  3. 渲染测试、交互测试、状态测试
  4. 无障碍测试
  5. 视觉回归测试
  6. 性能测试

请编写全面且可靠的组件测试。
```

### 集成测试
```
我要编写集成测试，请基于以下rules进行开发：
- 主要规则：02-component-architecture-rules.md（测试策略）
- 特别注意：
  1. 使用Cypress进行E2E测试
  2. 用户完整流程测试
  3. API集成测试
  4. 多语言功能测试
  5. 移动端测试
  6. 性能基准测试

请确保系统整体功能的可靠性。
```

## 📋 使用说明

### 如何使用提示词
1. **选择对应功能**：根据要开发的功能选择相应提示词
2. **复制完整提示词**：包含规则引用和特别注意事项
3. **根据需要调整**：可以根据具体需求微调提示词
4. **严格遵循规则**：确保开发过程中严格按照引用的规则执行

### 提示词优化建议
- 明确指定主要和辅助规则文件
- 列出具体的技术要求和注意事项
- 强调设计一致性和用户体验
- 包含性能和SEO考虑
- 明确质量标准和验收条件

### 常见问题
**Q: 如何选择主要规则和辅助规则？**
A: 主要规则是功能直接相关的规范，辅助规则是设计系统、架构等支撑性规范。

**Q: 可以同时引用多个规则文件吗？**
A: 可以，但建议不超过3个，保持提示词的清晰性。

**Q: 如何确保开发质量？**
A: 严格按照规则执行，进行代码审查，编写测试，性能检查。
