# ============================================================================
# 玄学多语言网站 - 环境变量配置模板
# 基于 Cloudflare 技术栈：D1 + Workers + R2 + KV
#
# 使用说明：
# 1. 复制此文件为 .env.local (本地开发)
# 2. 填入真实的API密钥和配置信息
# 3. 生产环境在部署平台Dashboard中配置
# ============================================================================

# 应用基础配置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Mystical Website"

# ============================================================================
# 数据库配置 - Cloudflare D1
# ============================================================================
# 本地开发使用本地SQLite数据库
DATABASE_URL="file:./dev.db"

# Cloudflare D1配置（通过wrangler.toml管理）
# 生产环境通过Cloudflare Workers绑定访问
# 数据库绑定名称: DB
# 数据库名称: tarot-seo-db

# ============================================================================
# 缓存配置 - Upstash Redis + Cloudflare KV
# ============================================================================
# 本地开发Redis（可选）
REDIS_URL="redis://localhost:6379"

# Upstash Redis（免费层：10K请求/天，256MB存储）
# 注册地址: https://upstash.com/
UPSTASH_REDIS_REST_URL="your-upstash-redis-url"
UPSTASH_REDIS_REST_TOKEN="your-upstash-redis-token"

# ============================================================================
# 认证配置
# ============================================================================
# 生产环境必须使用强密码
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"
NEXTAUTH_URL="http://localhost:3000"

# ============================================================================
# AI服务配置 - 多AI提供商策略
# ============================================================================
# 通义千问 (阿里云 - 主要AI服务)
# 注册地址: https://dashscope.aliyun.com/
QWEN_API_KEY="your-qwen-api-key"
QWEN_API_URL="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# 豆包 (字节跳动 - 备用AI服务)
# 注册地址: https://www.volcengine.com/product/doubao
DOUBAO_API_KEY="your-doubao-api-key"
DOUBAO_API_URL="https://ark.cn-beijing.volces.com/api/v3/chat/completions"

# 智谱AI (清华 - 多语言支持)
# 注册地址: https://open.bigmodel.cn/
ZHIPU_API_KEY="your-zhipu-api-key"
ZHIPU_API_URL="https://open.bigmodel.cn/api/paas/v4/chat/completions"

# ============================================================================
# 文件存储 - Cloudflare R2
# ============================================================================
# Cloudflare R2对象存储（S3兼容）
# 配置地址: https://dash.cloudflare.com/
CLOUDFLARE_R2_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_R2_ACCESS_KEY_ID="your-r2-access-key"
CLOUDFLARE_R2_SECRET_ACCESS_KEY="your-r2-secret-key"
CLOUDFLARE_R2_BUCKET_NAME="tarot-seo-assets"

# Cloudflare Images（可选，用于图片优化）
CLOUDFLARE_IMAGES_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_IMAGES_API_TOKEN="your-images-api-token"

# ============================================================================
# 监控和分析
# ============================================================================
# Sentry错误监控
# 注册地址: https://sentry.io/
SENTRY_DSN="your-sentry-dsn"
SENTRY_ORG="your-sentry-org"
SENTRY_PROJECT="your-sentry-project"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"

# Umami网站分析（开源，隐私友好）
# 部署地址: https://umami.is/
NEXT_PUBLIC_UMAMI_WEBSITE_ID="your-umami-website-id"
NEXT_PUBLIC_UMAMI_URL="https://umami.mystical-website.com"

# ============================================================================
# 邮件服务
# ============================================================================
# Gmail SMTP配置（推荐使用应用专用密码）
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# ============================================================================
# 社交媒体集成
# ============================================================================
NEXT_PUBLIC_FACEBOOK_APP_ID="your-facebook-app-id"
NEXT_PUBLIC_TWITTER_HANDLE="@mystical_website"
NEXT_PUBLIC_INSTAGRAM_HANDLE="mystical_website"

# ============================================================================
# SEO配置
# ============================================================================
# Google Search Console验证
NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION="your-google-site-verification"
# Bing Webmaster Tools验证
NEXT_PUBLIC_BING_SITE_VERIFICATION="your-bing-site-verification"

# ============================================================================
# Cloudflare部署配置
# ============================================================================
# Cloudflare API配置（用于自动化部署）
CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"
CLOUDFLARE_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_ZONE_ID="your-cloudflare-zone-id"

# ============================================================================
# 开发工具配置
# ============================================================================
# Bundle分析工具
ANALYZE=false
# 跳过环境变量验证（开发时可设为true）
SKIP_ENV_VALIDATION=false
# Wrangler遥测数据
WRANGLER_SEND_METRICS=false

# ============================================================================
# 部署配置（在部署平台Dashboard中配置，不在代码中）
# ============================================================================
# Vercel部署配置（如果使用Vercel）
# VERCEL_TOKEN="your-vercel-token"
# VERCEL_ORG_ID="your-vercel-org-id"
# VERCEL_PROJECT_ID="your-vercel-project-id"

# Cloudflare Pages部署配置（推荐）
# 通过Cloudflare Dashboard配置环境变量
