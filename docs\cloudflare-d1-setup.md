# 🗄️ Cloudflare D1数据库完整指南

## 📋 概述

本项目使用Cloudflare D1作为主数据库，这是一个基于SQLite的边缘数据库，提供全球分布式存储和低延迟访问。

## ✨ D1优势

### 🚀 技术优势
- **边缘分布**：数据存储在全球边缘节点，降低延迟
- **自动扩展**：无需管理服务器，自动处理负载
- **SQLite兼容**：使用熟悉的SQL语法
- **JSON支持**：原生JSON函数，适合复杂数据结构
- **全球读取副本**：自动在全球创建只读副本

### 💰 成本优势
- **免费额度**：100K读取/天，50K写入/天
- **按使用付费**：超出免费额度后按实际使用计费
- **无基础设施成本**：无需维护数据库服务器

## 🛠️ 快速设置

### 1️⃣ 安装Wrangler CLI

```bash
# 全局安装（推荐）
npm install -g wrangler

# 或使用项目本地版本
npm install wrangler --save-dev
```

### 2️⃣ 登录Cloudflare

```bash
wrangler login
```

### 3️⃣ 创建D1数据库

```bash
# 创建数据库（推荐使用东北美地区）
wrangler d1 create tarot-seo-db --location=enam

# 或使用npm脚本
npm run d1:create
```

### 4️⃣ 配置数据库连接

将创建数据库时返回的信息添加到 `wrangler.toml`：

```toml
[[d1_databases]]
binding = "DB"
database_name = "tarot-seo-db"
database_id = "your-database-id-here"  # 替换为实际ID
location = "enam"
```

### 5️⃣ 初始化数据库

```bash
# 生成Prisma客户端
npm run db:generate

# 应用数据库迁移
npm run d1:migrate:local   # 本地测试
npm run d1:migrate:remote  # 生产环境
```

## 📊 数据库架构

### 核心表结构
我们的数据库包含以下核心表：

- **`users`** - 用户基础信息和偏好设置
- **`blog_posts`** - 博客文章（支持多语言和SEO优化）
- **`test_results`** - 测试结果（支持分享和历史记录）
- **`comments`** - 评论系统（支持嵌套回复）
- **`user_favorites`** - 用户收藏
- **`blog_views`** - 浏览统计
- **`user_sessions`** - 用户会话管理
- **`user_verifications`** - 邮箱验证和密码重置

### 数据库模型
详细的数据库模型定义请查看：`prisma/schema.prisma`

### JSON数据存储
D1原生支持JSON函数，我们利用这个特性存储复杂数据：

- **标签数组**：`tags` 字段存储为 `["tag1", "tag2"]`
- **关键词数组**：`keywords` 字段存储为 `["keyword1", "keyword2"]`
- **元数据对象**：`metadata` 字段存储为 `{"aiGenerated": true, "readingLevel": "intermediate"}`

## 🔧 常用操作

### 本地开发
```bash
# 查看本地数据
wrangler d1 execute tarot-seo-db --local --command="SELECT * FROM users LIMIT 5"

# 执行SQL文件
wrangler d1 execute tarot-seo-db --local --file=./schema.sql

# 启动本地开发
npm run dev  # 自动连接本地D1数据库
```

### 生产环境
```bash
# 查看生产数据
wrangler d1 execute tarot-seo-db --remote --command="SELECT COUNT(*) FROM blog_posts"

# 备份数据库
wrangler d1 export tarot-seo-db --output=./backup.sql

# 部署应用
npm run deploy
```

## 📝 JSON数据查询

### 标签查询示例
```sql
-- 查询包含特定标签的文章
SELECT * FROM blog_posts
WHERE json_extract(tags, '$') LIKE '%"react"%'
AND status = 'PUBLISHED';

-- 获取标签数量
SELECT title, json_array_length(tags) as tag_count
FROM blog_posts;
```

### 元数据查询示例
```sql
-- 查询AI生成的文章
SELECT * FROM blog_posts
WHERE json_extract(metadata, '$.aiGenerated') = true;

-- 按阅读难度筛选
SELECT * FROM blog_posts
WHERE json_extract(metadata, '$.readingLevel') = 'intermediate';
```

## ⚡ 性能优化

### 索引策略
```sql
-- 博客文章核心索引
CREATE INDEX idx_blog_posts_locale_category ON blog_posts(locale, category);
CREATE INDEX idx_blog_posts_status_published ON blog_posts(status, publishedAt);
CREATE INDEX idx_blog_posts_slug ON blog_posts(slug);

-- 测试结果索引
CREATE INDEX idx_test_results_user_type ON test_results(userId, testType);
CREATE INDEX idx_test_results_share_token ON test_results(shareToken);

-- 用户相关索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_comments_post_created ON comments(postId, createdAt);
```

### 查询优化技巧
- **分页查询**：使用 `LIMIT` 和 `OFFSET`
- **JSON查询**：利用 `json_extract()` 函数
- **批量操作**：使用D1的batch API
- **缓存策略**：结合Cloudflare边缘缓存

### 全球读取副本（可选）
```bash
# 启用全球读取副本（Beta功能）
wrangler d1 read-replication enable tarot-seo-db
```

**优势**：
- 全球分布的只读副本
- 降低查询延迟
- 自动同步数据
- 透明路由查询

## 🚀 部署和维护

### 开发工作流
```bash
# 1. 本地开发
npm run dev                    # 启动开发服务器
npm run d1:migrate:local      # 应用本地迁移

# 2. 测试验证
wrangler d1 execute tarot-seo-db --local --command="SELECT 1"

# 3. 生产部署
npm run d1:migrate:remote     # 应用生产迁移
npm run deploy                # 部署到Cloudflare
```

### 备份和恢复
```bash
# 创建备份
wrangler d1 export tarot-seo-db --output=./backup-$(date +%Y%m%d).sql

# 恢复备份
wrangler d1 execute tarot-seo-db --file=./backup-20241201.sql

# Time Travel恢复（付费功能）
wrangler d1 time-travel restore tarot-seo-db --timestamp=2024-12-01T10:00:00Z
```

### 监控和告警
- **Cloudflare Dashboard**：查看D1使用情况和性能指标
- **查询监控**：监控查询性能和错误率
- **使用量告警**：设置接近免费额度的告警
- **性能分析**：使用Sentry监控应用性能

## 🔧 故障排除

### 常见问题解决

#### 连接问题
```bash
# 检查数据库配置
cat wrangler.toml | grep -A 5 "d1_databases"

# 验证数据库ID
wrangler d1 list

# 测试连接
wrangler d1 execute tarot-seo-db --local --command="SELECT 1"
```

#### 查询性能问题
```bash
# 检查表结构
wrangler d1 execute tarot-seo-db --local --command="PRAGMA table_info(blog_posts)"

# 分析查询计划
wrangler d1 execute tarot-seo-db --local --command="EXPLAIN QUERY PLAN SELECT * FROM blog_posts WHERE locale = 'en'"

# 检查索引使用
wrangler d1 execute tarot-seo-db --local --command="PRAGMA index_list(blog_posts)"
```

#### JSON数据问题
```bash
# 验证JSON格式
wrangler d1 execute tarot-seo-db --local --command="SELECT json_valid(tags) FROM blog_posts LIMIT 5"

# 检查JSON数据
wrangler d1 execute tarot-seo-db --local --command="SELECT json_extract(metadata, '$.aiGenerated') FROM blog_posts LIMIT 5"
```

## ✅ 设置检查清单

- [ ] 安装并配置Wrangler CLI
- [ ] 创建D1数据库
- [ ] 配置wrangler.toml
- [ ] 设置环境变量
- [ ] 运行数据库迁移
- [ ] 测试本地开发环境
- [ ] 验证JSON数据处理
- [ ] 创建必要索引
- [ ] 部署到生产环境
- [ ] 配置监控和告警
- [ ] 设置备份策略
- [ ] 启用读取副本（可选）

## 📚 相关文档

- **项目规范**：`.augment/rules/07-database-api-rules.md`
- **环境配置**：`docs/ENV_SETUP.md`
- **数据库模型**：`prisma/schema.prisma`
- **开发指南**：`docs/DEVELOPMENT_GUIDE.md`

## 🔗 外部资源

- [Cloudflare D1文档](https://developers.cloudflare.com/d1/)
- [Wrangler CLI文档](https://developers.cloudflare.com/workers/wrangler/)
- [SQLite JSON函数](https://www.sqlite.org/json1.html)
- [Prisma D1适配器](https://www.prisma.io/docs/orm/overview/databases/cloudflare-d1)
