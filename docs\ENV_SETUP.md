# 🔧 环境变量配置指南

## 📁 文件结构

```bash
.env.development    # 开发环境配置（Git跟踪）
.env.production     # 生产环境配置（Git跟踪）
.env.example        # 配置模板文档（Git跟踪）
.env.backup.supabase # 原始Supabase配置备份
```

## 🚀 快速开始

### 1. 开发环境设置
```bash
# 1. 编辑开发环境配置
code .env.development

# 2. 填入你的真实API密钥
QWEN_API_KEY="your-real-qwen-api-key"
SENTRY_DSN="your-real-sentry-dsn"
# ... 其他真实配置

# 3. 启动开发服务器
npm run dev
```

### 2. 生产环境设置
```bash
# 1. 编辑生产环境配置
code .env.production

# 2. 填入生产环境的API密钥
QWEN_API_KEY="your-production-qwen-api-key"
NEXT_PUBLIC_APP_URL="https://your-domain.com"
# ... 其他生产配置

# 3. 部署到生产环境
npm run build
```

## 🔄 自动环境检测

Next.js会根据 `NODE_ENV` 自动加载对应的环境文件：

- **开发环境**: `npm run dev` → 自动加载 `.env.development`
- **生产环境**: `npm run build` → 自动加载 `.env.production`

## 🛡️ 安全策略

### Git管理
- ✅ `.env.development` - Git跟踪（可包含开发API密钥）
- ✅ `.env.production` - Git跟踪（可包含生产API密钥模板）
- ✅ `.env.example` - Git跟踪（仅包含示例和文档）
- ❌ `.env.backup.supabase` - Git忽略（备份文件）

### 部署平台覆盖
在生产部署时，建议通过部署平台的环境变量功能覆盖敏感配置：

**Vercel Dashboard:**
```bash
QWEN_API_KEY=your-real-production-key
NEXTAUTH_SECRET=your-strong-production-secret
```

**Cloudflare Pages:**
```bash
QWEN_API_KEY=your-real-production-key
NEXTAUTH_SECRET=your-strong-production-secret
```

## 📝 配置变量说明

### 必需配置
```bash
# 应用基础
NODE_ENV=development|production
NEXT_PUBLIC_APP_URL=http://localhost:3000|https://your-domain.com
NEXTAUTH_SECRET=your-secret-key

# AI服务
QWEN_API_KEY=your-qwen-api-key
```

### 可选配置
```bash
# 监控
SENTRY_DSN=your-sentry-dsn
NEXT_PUBLIC_UMAMI_WEBSITE_ID=your-umami-id

# 存储
CLOUDFLARE_R2_ACCOUNT_ID=your-account-id
UPSTASH_REDIS_REST_URL=your-redis-url
```

## 🔍 故障排除

### 环境变量不生效
1. 检查文件名是否正确
2. 重启开发服务器
3. 检查变量名拼写

### API密钥错误
1. 确认密钥有效性
2. 检查是否有特殊字符
3. 验证API服务状态

## 📚 更多信息

详细的环境变量规范请参考：`.augment/rules/09-environment-variables-rules.md`
