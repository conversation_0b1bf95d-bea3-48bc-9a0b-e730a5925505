# ============================================================================
# 玄学多语言网站 - 生产环境配置
# 基于 Cloudflare 技术栈：D1 + Workers + R2 + KV
# ============================================================================

# 应用基础配置
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME="Mystical Website"

# ============================================================================
# 数据库配置 - Cloudflare D1
# ============================================================================
# 生产环境使用Cloudflare D1远程数据库
DATABASE_URL="file:./dev.db"

# Cloudflare D1配置（通过wrangler.toml管理）
# 生产环境通过Cloudflare Workers绑定访问
# 数据库绑定名称: DB
# 数据库名称: tarot-seo-db
# 数据库ID: 87945106-1e0d-4375-967c-d89324987198

# ============================================================================
# 缓存配置 - Upstash Redis + Cloudflare KV
# ============================================================================
# 生产环境不使用本地Redis
# REDIS_URL="redis://localhost:6379"

# Upstash Redis（免费层：10K请求/天，256MB存储）
UPSTASH_REDIS_REST_URL="your-production-upstash-redis-url"
UPSTASH_REDIS_REST_TOKEN="your-production-upstash-redis-token"

# ============================================================================
# 认证配置
# ============================================================================
NEXTAUTH_SECRET="your-production-secret-key-32-chars-minimum"
NEXTAUTH_URL="https://your-domain.com"

# ============================================================================
# AI服务配置 - 多AI提供商策略
# ============================================================================
# 通义千问 (阿里云 - 主要AI服务)
QWEN_API_KEY="your-production-qwen-api-key"
QWEN_API_URL="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# 豆包 (字节跳动 - 备用AI服务)
DOUBAO_API_KEY="your-production-doubao-api-key"
DOUBAO_API_URL="https://ark.cn-beijing.volces.com/api/v3/chat/completions"

# 智谱AI (清华 - 多语言支持)
ZHIPU_API_KEY="your-production-zhipu-api-key"
ZHIPU_API_URL="https://open.bigmodel.cn/api/paas/v4/chat/completions"

# ============================================================================
# 文件存储 - Cloudflare R2
# ============================================================================
# Cloudflare R2对象存储（S3兼容）
CLOUDFLARE_R2_ACCOUNT_ID="your-production-cloudflare-account-id"
CLOUDFLARE_R2_ACCESS_KEY_ID="your-production-r2-access-key"
CLOUDFLARE_R2_SECRET_ACCESS_KEY="your-production-r2-secret-key"
CLOUDFLARE_R2_BUCKET_NAME="tarot-seo-assets"

# Cloudflare Images（可选，用于图片优化）
CLOUDFLARE_IMAGES_ACCOUNT_ID="your-production-cloudflare-account-id"
CLOUDFLARE_IMAGES_API_TOKEN="your-production-images-api-token"

# ============================================================================
# 监控和分析
# ============================================================================
# Sentry错误监控
SENTRY_DSN="your-production-sentry-dsn"
SENTRY_ORG="your-production-sentry-org"
SENTRY_PROJECT="your-production-sentry-project"
SENTRY_AUTH_TOKEN="your-production-sentry-auth-token"

# Umami网站分析（开源，隐私友好）
NEXT_PUBLIC_UMAMI_WEBSITE_ID="your-production-umami-website-id"
NEXT_PUBLIC_UMAMI_URL="https://umami.mystical-website.com"

# ============================================================================
# 邮件服务
# ============================================================================
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-production-app-password"

# ============================================================================
# 社交媒体集成
# ============================================================================
NEXT_PUBLIC_FACEBOOK_APP_ID="your-production-facebook-app-id"
NEXT_PUBLIC_TWITTER_HANDLE="@mystical_website"
NEXT_PUBLIC_INSTAGRAM_HANDLE="mystical_website"

# ============================================================================
# SEO配置
# ============================================================================
NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION="your-production-google-site-verification"
NEXT_PUBLIC_BING_SITE_VERIFICATION="your-production-bing-site-verification"

# ============================================================================
# Cloudflare部署配置
# ============================================================================
CLOUDFLARE_API_TOKEN="your-production-cloudflare-api-token"
CLOUDFLARE_ACCOUNT_ID="your-production-cloudflare-account-id"
CLOUDFLARE_ZONE_ID="your-production-cloudflare-zone-id"

# ============================================================================
# 开发工具配置
# ============================================================================
ANALYZE=false
SKIP_ENV_VALIDATION=false
WRANGLER_SEND_METRICS=false

# 生产环境安全配置
DEBUG=false
SKIP_AUTH_VALIDATION=false

# ============================================================================
# 生产环境说明
# ============================================================================
# 本文件包含生产环境的配置模板
# 实际部署时，敏感信息应该通过以下方式配置：
# 1. Vercel Dashboard环境变量
# 2. Cloudflare Pages环境变量  
# 3. 其他部署平台的环境变量配置
# 
# 安全提醒：
# - 所有API密钥必须使用生产环境专用密钥
# - NEXTAUTH_SECRET必须是强密码（32字符以上）
# - 定期轮换敏感密钥
