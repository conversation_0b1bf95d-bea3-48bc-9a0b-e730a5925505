// Cloudflare D1适配的数据库操作
// Database operations adapted for Cloudflare D1

import { PostStatus, TestType, type BlogPost, type TestResult, type User } from '@prisma/client'
import { D1JsonUtils, D1QueryBuilder, type D1Database } from './d1'

// 博客文章相关操作（D1适配版本）
// Blog post operations (D1 adapted)

export async function getBlogPostsD1(
  db: D1Database,
  {
    locale,
    category,
    status = PostStatus.PUBLISHED,
    page = 1,
    limit = 10,
    search,
    tags,
  }: {
    locale?: string
    category?: string
    status?: PostStatus
    page?: number
    limit?: number
    search?: string
    tags?: string[]
  }
) {
  const offset = (page - 1) * limit
  
  const { query, bindings } = D1QueryBuilder.buildBlogSearchQuery({
    locale,
    category,
    tags,
    status,
    limit,
    offset,
  })

  // 如果有搜索词，修改查询
  let finalQuery = query
  let finalBindings = [...bindings]
  
  if (search) {
    finalQuery = finalQuery.replace(
      'WHERE 1=1',
      `WHERE 1=1 AND (
        title LIKE ? OR 
        content LIKE ? OR 
        json_extract(tags, '$') LIKE ?
      )`
    )
    finalBindings.unshift(`%${search}%`, `%${search}%`, `%"${search}"%`)
  }

  // 执行查询
  const results = await db.prepare(finalQuery).bind(...finalBindings).all()
  
  // 获取总数
  const countQuery = finalQuery
    .replace(/SELECT[\s\S]*?FROM/, 'SELECT COUNT(*) as count FROM')
    .replace(/ORDER BY[\s\S]*$/, '')
  const countResult = await db.prepare(countQuery).bind(...finalBindings).first<{ count: number }>()
  const total = countResult?.count || 0

  // 处理结果，转换JSON字段
  const posts = results.results?.map(post => ({
    ...post,
    tags: D1JsonUtils.jsonToArray(post.tags as string),
    keywords: D1JsonUtils.jsonToArray(post.keywords as string),
    metadata: post.metadata ? D1JsonUtils.jsonToObject(post.metadata as string) : null,
  })) || []

  return {
    posts,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1,
    },
  }
}

export async function getBlogPostBySlugD1(db: D1Database, slug: string, locale?: string) {
  let query = `
    SELECT * FROM blog_posts 
    WHERE slug = ? AND status = ?
  `
  const bindings: unknown[] = [slug, PostStatus.PUBLISHED]

  if (locale) {
    query += ` AND locale = ?`
    bindings.push(locale)
  }

  const result = await db.prepare(query).bind(...bindings).first()
  
  if (!result) return null

  // 转换JSON字段
  return {
    ...result,
    tags: D1JsonUtils.jsonToArray(result.tags as string),
    keywords: D1JsonUtils.jsonToArray(result.keywords as string),
    metadata: result.metadata ? D1JsonUtils.jsonToObject(result.metadata as string) : null,
  }
}

export async function createBlogPostD1(
  db: D1Database,
  data: {
    title: string
    content: string
    excerpt?: string
    locale: string
    category: string
    tags: string[]
    coverImage?: string
    seoTitle?: string
    seoDescription?: string
    keywords: string[]
    status?: PostStatus
    metadata?: Record<string, unknown>
  }
) {
  // 生成 slug
  const slug = data.title
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fa5]+/g, '-')
    .replace(/^-+|-+$/g, '')

  // 计算阅读时间（基于内容长度）
  const readingTime = Math.ceil(data.content.replace(/<[^>]*>/g, '').length / 200)

  // 生成ID
  const id = crypto.randomUUID()

  const query = `
    INSERT INTO blog_posts (
      id, title, slug, content, excerpt, coverImage, locale, category,
      tags, seoTitle, seoDescription, keywords, status, readingTime,
      metadata, createdAt, updatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
  `

  const result = await db.prepare(query).bind(
    id,
    data.title,
    slug,
    data.content,
    data.excerpt || null,
    data.coverImage || null,
    data.locale,
    data.category,
    D1JsonUtils.arrayToJson(data.tags),
    data.seoTitle || null,
    data.seoDescription || null,
    D1JsonUtils.arrayToJson(data.keywords),
    data.status || PostStatus.DRAFT,
    readingTime,
    data.metadata ? D1JsonUtils.objectToJson(data.metadata) : null
  ).run()

  return { id, slug, readingTime, ...result }
}

export async function updateBlogPostD1(
  db: D1Database,
  id: string,
  data: Partial<{
    title: string
    content: string
    excerpt: string
    tags: string[]
    keywords: string[]
    status: PostStatus
    metadata: Record<string, unknown>
  }>
) {
  const updates: string[] = []
  const bindings: unknown[] = []

  if (data.title) {
    updates.push('title = ?')
    bindings.push(data.title)
  }
  if (data.content) {
    updates.push('content = ?')
    bindings.push(data.content)
  }
  if (data.excerpt !== undefined) {
    updates.push('excerpt = ?')
    bindings.push(data.excerpt)
  }
  if (data.tags) {
    updates.push('tags = ?')
    bindings.push(D1JsonUtils.arrayToJson(data.tags))
  }
  if (data.keywords) {
    updates.push('keywords = ?')
    bindings.push(D1JsonUtils.arrayToJson(data.keywords))
  }
  if (data.status) {
    updates.push('status = ?')
    bindings.push(data.status)
  }
  if (data.metadata) {
    updates.push('metadata = ?')
    bindings.push(D1JsonUtils.objectToJson(data.metadata))
  }

  updates.push('updatedAt = datetime("now")')
  bindings.push(id)

  const query = `UPDATE blog_posts SET ${updates.join(', ')} WHERE id = ?`
  
  return await db.prepare(query).bind(...bindings).run()
}

// 测试结果相关操作（D1适配版本）
export async function createTestResultD1(
  db: D1Database,
  data: {
    userId?: string
    testType: TestType
    answers: Record<string, unknown>
    result: Record<string, unknown>
    shareToken?: string
    isPublic?: boolean
  }
) {
  const id = crypto.randomUUID()
  
  const query = `
    INSERT INTO test_results (
      id, userId, testType, answers, result, shareToken, isPublic, createdAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
  `

  const queryResult = await db.prepare(query).bind(
    id,
    data.userId || null,
    data.testType,
    D1JsonUtils.objectToJson(data.answers),
    D1JsonUtils.objectToJson(data.result),
    data.shareToken || null,
    data.isPublic || false
  ).run()

  return { id, ...queryResult }
}

export async function getTestResultD1(db: D1Database, id: string) {
  const query = `
    SELECT tr.*, u.username, u.avatar 
    FROM test_results tr
    LEFT JOIN users u ON tr.userId = u.id
    WHERE tr.id = ?
  `
  
  const result = await db.prepare(query).bind(id).first()
  
  if (!result) return null

  return {
    ...result,
    answers: D1JsonUtils.jsonToObject(result.answers as string),
    result: D1JsonUtils.jsonToObject(result.result as string),
  }
}

export async function getTestResultByShareTokenD1(db: D1Database, shareToken: string) {
  const query = `
    SELECT tr.*, u.username, u.avatar 
    FROM test_results tr
    LEFT JOIN users u ON tr.userId = u.id
    WHERE tr.shareToken = ? AND tr.isPublic = true
  `
  
  const result = await db.prepare(query).bind(shareToken).first()
  
  if (!result) return null

  return {
    ...result,
    answers: D1JsonUtils.jsonToObject(result.answers as string),
    result: D1JsonUtils.jsonToObject(result.result as string),
  }
}

// 用户相关操作（D1适配版本）
export async function createUserD1(
  db: D1Database,
  data: {
    email: string
    username?: string
    avatar?: string
    locale?: string
    theme?: string
  }
) {
  const id = crypto.randomUUID()
  
  const query = `
    INSERT INTO users (
      id, email, username, avatar, locale, theme, createdAt, updatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
  `

  const result = await db.prepare(query).bind(
    id,
    data.email,
    data.username || null,
    data.avatar || null,
    data.locale || 'en',
    data.theme || 'light'
  ).run()

  return { id, ...result }
}

export async function getUserByEmailD1(db: D1Database, email: string) {
  const query = `SELECT * FROM users WHERE email = ?`
  return await db.prepare(query).bind(email).first()
}

export async function getUserByIdD1(db: D1Database, id: string) {
  const query = `SELECT * FROM users WHERE id = ?`
  return await db.prepare(query).bind(id).first()
}

// 收藏相关操作（D1适配版本）
export async function addToFavoritesD1(db: D1Database, userId: string, postId: string) {
  const id = crypto.randomUUID()
  
  const query = `
    INSERT INTO user_favorites (id, userId, postId, createdAt)
    VALUES (?, ?, ?, datetime('now'))
  `
  
  return await db.prepare(query).bind(id, userId, postId).run()
}

export async function removeFromFavoritesD1(db: D1Database, userId: string, postId: string) {
  const query = `DELETE FROM user_favorites WHERE userId = ? AND postId = ?`
  return await db.prepare(query).bind(userId, postId).run()
}

// 评论相关操作（D1适配版本）
export async function createCommentD1(
  db: D1Database,
  data: {
    content: string
    userId?: string
    postId: string
    parentId?: string
  }
) {
  const id = crypto.randomUUID()
  
  const query = `
    INSERT INTO comments (
      id, content, userId, postId, parentId, createdAt, updatedAt
    ) VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
  `

  const result = await db.prepare(query).bind(
    id,
    data.content,
    data.userId || null,
    data.postId,
    data.parentId || null
  ).run()

  return { id, ...result }
}
