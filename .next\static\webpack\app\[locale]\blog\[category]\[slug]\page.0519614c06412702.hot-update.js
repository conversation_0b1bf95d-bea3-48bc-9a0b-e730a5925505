"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/blog/[category]/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/blog/TableOfContents.tsx":
/*!*************************************************!*\
  !*** ./src/components/blog/TableOfContents.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileTableOfContents: function() { return /* binding */ MobileTableOfContents; },\n/* harmony export */   TableOfContents: function() { return /* binding */ TableOfContents; },\n/* harmony export */   generateTableOfContents: function() { return /* binding */ generateTableOfContents; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_List_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_List_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_List_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ TableOfContents,MobileTableOfContents,generateTableOfContents auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction TableOfContents(param) {\n    let { items, className, variant = \"floating\", collapsible = false } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"blog\");\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                if (entry.isIntersecting) {\n                    setActiveId(entry.target.id);\n                }\n            });\n        }, {\n            rootMargin: \"-20% 0% -35% 0%\",\n            threshold: 0\n        });\n        // 观察所有标题元素\n        items.forEach((item)=>{\n            const element = document.getElementById(item.anchor);\n            if (element) {\n                observer.observe(element);\n            }\n        });\n        return ()=>{\n            observer.disconnect();\n        };\n    }, [\n        items\n    ]);\n    const handleClick = (anchor)=>{\n        const element = document.getElementById(anchor);\n        if (element) {\n            const offsetTop = element.offsetTop - 100; // 留出一些空间\n            window.scrollTo({\n                top: offsetTop,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const containerClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600 rounded-lg\", {\n        // 浮动样式\n        \"fixed left-6 top-1/2 transform -translate-y-1/2 max-w-xs w-64 p-4 shadow-mystical z-40 hidden xl:block\": variant === \"floating\",\n        // 侧边栏样式\n        \"sticky top-6 p-4\": variant === \"sidebar\",\n        // 移动端样式\n        \"p-4 mb-6\": variant === \"mobile\"\n    }, className);\n    const titleClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center justify-between text-sm font-bold text-mystical-900 dark:text-white mb-3\", {\n        \"cursor-pointer\": collapsible\n    });\n    if (items.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: containerClasses,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: titleClasses,\n                onClick: collapsible ? ()=>setIsCollapsed(!isCollapsed) : undefined,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_List_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t(\"tableOfContents\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    collapsible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"p-1 hover:bg-mystical-100 dark:hover:bg-dark-700 rounded\",\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_List_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_List_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-mystical-200 dark:border-dark-600 mb-3\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"space-y-1\",\n                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TOCItem, {\n                        item: item,\n                        activeId: activeId,\n                        onClick: handleClick\n                    }, item.id, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(TableOfContents, \"3ZEGUVdh92vOMN+FQ7Xey1L6Hbo=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations\n    ];\n});\n_c = TableOfContents;\n// 目录项组件\nfunction TOCItem(param) {\n    let { item, activeId, onClick } = param;\n    const isActive = activeId === item.anchor;\n    const itemClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"block w-full text-left text-sm py-1 px-2 rounded transition-all duration-200\", \"border-l-2 transition-colors\", {\n        // 根据标题级别设置缩进\n        \"pl-2\": item.level === 1,\n        \"pl-4\": item.level === 2,\n        \"pl-6\": item.level === 3,\n        \"pl-8\": item.level === 4,\n        \"pl-10\": item.level === 5,\n        \"pl-12\": item.level === 6,\n        // 激活状态\n        \"text-mystical-800 dark:text-white font-medium border-l-mystical-500 bg-mystical-50 dark:bg-dark-700\": isActive,\n        // 非激活状态\n        \"text-mystical-600 dark:text-mystical-400 border-l-transparent hover:text-mystical-700 dark:hover:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700\": !isActive\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: itemClasses,\n                onClick: ()=>onClick(item.anchor),\n                title: item.title,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"line-clamp-2\",\n                    children: item.title\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            item.children && item.children.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"mt-1 space-y-1\",\n                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TOCItem, {\n                        item: child,\n                        activeId: activeId,\n                        onClick: onClick\n                    }, child.id, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_c1 = TOCItem;\n// 移动端可折叠的目录组件\nfunction MobileTableOfContents(param) {\n    let { items, className } = param;\n    _s1();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (items.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"xl:hidden\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableOfContents, {\n            items: items,\n            variant: \"mobile\",\n            collapsible: true\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\blog\\\\TableOfContents.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n_s1(MobileTableOfContents, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c2 = MobileTableOfContents;\n// 生成目录数据的工具函数\nfunction generateTableOfContents(content) {\n    const headingRegex = /^(#{1,6})\\s+(.+)$/gm;\n    const headings = [];\n    let match;\n    while((match = headingRegex.exec(content)) !== null){\n        const level = match[1].length;\n        const title = match[2].trim();\n        const anchor = title.toLowerCase().replace(/[^\\w\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n        headings.push({\n            id: \"heading-\".concat(headings.length + 1),\n            title,\n            level,\n            anchor\n        });\n    }\n    // 构建层级结构\n    return buildHierarchy(headings);\n}\nfunction buildHierarchy(headings) {\n    const result = [];\n    const stack = [];\n    for (const heading of headings){\n        // 找到合适的父级\n        while(stack.length > 0 && stack[stack.length - 1].level >= heading.level){\n            stack.pop();\n        }\n        if (stack.length === 0) {\n            // 顶级标题\n            result.push(heading);\n        } else {\n            // 子标题\n            const parent = stack[stack.length - 1];\n            if (!parent.children) {\n                parent.children = [];\n            }\n            parent.children.push(heading);\n        }\n        stack.push(heading);\n    }\n    return result;\n}\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TableOfContents\");\n$RefreshReg$(_c1, \"TOCItem\");\n$RefreshReg$(_c2, \"MobileTableOfContents\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/blog/TableOfContents.tsx\n"));

/***/ })

});