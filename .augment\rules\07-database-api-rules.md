---
type: "conditional_apply"
description: "数据库设计和API架构规范"
---

# 数据库设计和API架构规范

## 数据库架构设计

### Prisma Schema设计
```prisma
// 用户相关表
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String?  @unique
  avatar      String?
  locale      String   @default("en")
  theme       String   @default("light")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  testResults TestResult[]
  blogViews   BlogView[]
  comments    Comment[]
  favorites   UserFavorite[]
  sessions    UserSession[]
  verifications UserVerification[]

  @@map("users")
}

// 博客文章表（Cloudflare D1优化版本）
model BlogPost {
  id            String   @id @default(cuid())
  title         String
  slug          String   @unique
  content       String   // 支持富文本HTML内容
  excerpt       String?
  coverImage    String?
  locale        String
  category      String
  tags          String   // JSON数组格式: ["tag1", "tag2"]，使用D1 JSON函数查询
  status        PostStatus @default(DRAFT)
  publishedAt   DateTime?
  viewCount     Int      @default(0)
  readingTime   Int      @default(0) // 分钟数

  // SEO优化字段
  seoTitle      String?
  seoDescription String?
  keywords      String   // JSON数组格式: ["keyword1", "keyword2"]

  // 扩展元数据（JSON格式，存储AI生成信息等）
  metadata      String?  // JSON对象格式，利用D1原生JSON函数

  // 时间戳
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  views         BlogView[]
  comments      Comment[]
  favorites     UserFavorite[]

  // D1优化索引策略
  @@index([locale, category])
  @@index([status, publishedAt])
  @@index([locale, status, publishedAt])
  @@index([slug]) // 单独为slug创建索引，提高查询性能
  @@map("blog_posts")
}

// 测试结果表
model TestResult {
  id          String   @id @default(cuid())
  userId      String?
  testType    TestType
  answers     String   // 存储为JSON字符串
  result      String   // 存储为JSON字符串
  shareToken  String?  @unique
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())

  // 关联关系
  user        User?    @relation(fields: [userId], references: [id])

  @@map("test_results")
}

// 枚举类型
enum PostStatus {
  DRAFT
  PENDING
  SCHEDULED
  PUBLISHED
  ARCHIVED
  DELETED
}

enum TestType {
  TAROT
  ASTROLOGY
  NUMEROLOGY
  CRYSTAL
  PALMISTRY
  DREAMS
}

// 评论表
model Comment {
  id        String   @id @default(cuid())
  content   String
  userId    String?
  postId    String
  parentId  String?  // 回复评论的父评论ID
  isApproved Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  user      User?     @relation(fields: [userId], references: [id])
  post      BlogPost  @relation(fields: [postId], references: [id], onDelete: Cascade)
  parent    Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies   Comment[] @relation("CommentReplies")

  @@map("comments")
}

// 用户收藏表
model UserFavorite {
  id     String @id @default(cuid())
  userId String
  postId String
  createdAt DateTime @default(now())

  // 关联关系
  user   User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  post   BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)

  // 唯一约束：用户不能重复收藏同一篇文章
  @@unique([userId, postId])
  @@map("user_favorites")
}

// 博客浏览记录表
model BlogView {
  id        String   @id @default(cuid())
  postId    String
  userId    String?  // 可选，游客访问时为null
  ipAddress String?  // 用于统计唯一访问
  userAgent String?
  createdAt DateTime @default(now())

  // 关联关系
  post      BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User?    @relation(fields: [userId], references: [id])

  @@map("blog_views")
}

// 用户会话表
model UserSession {
  id           String   @id @default(cuid())
  userId       String
  sessionToken String   @unique
  refreshToken String   @unique
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关联关系
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// 用户验证表（邮箱验证、密码重置等）
model UserVerification {
  id        String   @id @default(cuid())
  userId    String?
  email     String?
  token     String   @unique
  type      VerificationType
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  // 关联关系
  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_verifications")
}

// 验证类型枚举
enum VerificationType {
  EMAIL_VERIFICATION
  PASSWORD_RESET
  ACCOUNT_DELETION
}
```

### 数据库优化策略
```typescript
// Cloudflare D1数据库性能优化配置
const DATABASE_OPTIMIZATION = {
  // 索引策略（D1基于SQLite）
  indexes: {
    blogPosts: [
      'slug',
      'locale',
      'category',
      'status',
      'publishedAt',
      ['locale', 'category'],
      ['status', 'publishedAt']
    ],

    testResults: [
      'userId',
      'testType',
      'shareToken',
      'createdAt',
      ['testType', 'createdAt']
    ],

    users: [
      'email',
      'username',
      'locale'
    ]
  },

  // 查询优化（适配D1边缘计算）
  queryOptimization: {
    blogList: 'SELECT with LIMIT/OFFSET pagination',
    testHistory: 'User-specific queries with proper indexing',
    popularPosts: 'ORDER BY viewCount with caching',
    relatedPosts: 'JSON_EXTRACT for tag matching'
  },

  // 缓存策略（结合Cloudflare边缘缓存）
  caching: {
    blogPosts: {
      ttl: '1 hour',
      keys: ['slug', 'category', 'locale'],
      invalidation: 'on update/delete',
      edgeCache: 'Cloudflare Cache API'
    },

    testResults: {
      ttl: '24 hours',
      keys: ['shareToken'],
      invalidation: 'manual',
      edgeCache: 'Worker KV for sharing'
    },

    userPreferences: {
      ttl: '6 hours',
      keys: ['userId'],
      invalidation: 'on preference update',
      edgeCache: 'Session storage'
    }
  },

  // D1特定优化
  d1Optimizations: {
    batchOperations: 'Use D1 batch API for bulk operations',
    jsonHandling: 'JSON_EXTRACT, JSON_SET, JSON_ARRAY_LENGTH for complex data',
    edgeDistribution: 'Leverage global edge distribution',
    readReplicas: 'Automatic read scaling across regions',
    queryLimits: 'Max 1000 queries per Worker invocation (paid), 50 (free)',
    connectionLimits: 'Max 6 simultaneous connections per Worker',
    jsonArrayQueries: 'Use json_each() for array expansion in WHERE IN clauses'
  }
};

// D1特定查询示例
const D1_QUERY_EXAMPLES = {
  // JSON数组查询
  tagSearch: `
    SELECT * FROM blog_posts
    WHERE json_extract(tags, '$') LIKE '%"react"%'
    AND status = 'PUBLISHED'
  `,

  // 使用JSON函数提取关键词
  keywordExtraction: `
    SELECT
      id,
      title,
      json_extract(keywords, '$[0]') as primary_keyword,
      json_array_length(keywords) as keyword_count
    FROM blog_posts
    WHERE locale = ?
  `,

  // 批量查询优化
  batchTagUpdate: `
    UPDATE blog_posts
    SET tags = json_set(tags, '$[#]', ?)
    WHERE id IN (SELECT value FROM json_each(?))
  `,

  // 元数据查询
  metadataQuery: `
    SELECT
      id,
      title,
      json_extract(metadata, '$.aiGenerated') as is_ai_generated,
      json_extract(metadata, '$.readingLevel') as reading_level
    FROM blog_posts
    WHERE json_extract(metadata, '$.aiGenerated') = true
  `
};
```

## API设计规范

### RESTful API架构
```typescript
// API路由设计
const API_ROUTES = {
  // 博客相关API（简化版）
  blog: {
    'GET /api/blog': {
      description: '获取博客文章列表',
      params: {
        page: 'number',
        limit: 'number',
        category: 'string',
        locale: 'string',
        search: 'string',
        status: 'PostStatus',
        tags: 'string[]'
      },
      response: 'PaginatedBlogPosts'
    },

    'GET /api/blog/[slug]': {
      description: '获取单篇文章详情',
      params: { slug: 'string' },
      response: 'BlogPostDetail'
    },

    'POST /api/blog': {
      description: '创建新文章（支持AI生成内容，D1优化）',
      body: {
        title: 'string',
        content: 'string',
        excerpt: 'string?',
        locale: 'string',
        category: 'string',
        tags: 'string[]', // 前端传数组，后端转换为JSON字符串存储
        coverImage: 'string?',
        seoTitle: 'string?',
        seoDescription: 'string?',
        keywords: 'string[]', // 前端传数组，后端转换为JSON字符串存储
        status: 'PostStatus?',
        metadata: 'object?' // 前端传对象，后端转换为JSON字符串存储
      },
      response: 'BlogPost',
      d1Optimization: {
        jsonConversion: '自动将数组和对象转换为JSON字符串',
        batchInsert: '支持批量插入操作',
        edgeCache: '利用Cloudflare边缘缓存'
      }
    },

    'PUT /api/blog/[id]': {
      description: '更新文章',
      params: { id: 'string' },
      body: 'Partial<BlogPostUpdate>',
      response: 'BlogPost'
    },

    'DELETE /api/blog/[id]': {
      description: '删除文章（软删除）',
      params: { id: 'string' },
      response: 'DeleteSuccess'
    },

    'POST /api/blog/[id]/view': {
      description: '记录文章浏览',
      params: { id: 'string' },
      body: { userId: 'string?', ipAddress: 'string?' },
      response: 'ViewRecorded'
    },

    'PATCH /api/blog/[id]/publish': {
      description: '发布文章',
      params: { id: 'string' },
      response: 'PublishSuccess'
    },

    'POST /api/blog/ai-generate': {
      description: 'AI生成文章内容',
      body: {
        topic: 'string',
        category: 'string',
        locale: 'string',
        keywords: 'string[]',
        length: 'short | medium | long'
      },
      response: 'AIGeneratedContent'
    }
  },

  // 测试相关API
  tests: {
    'POST /api/tests/start': {
      description: '开始新测试',
      body: { testType: 'TestType', userId: 'string?' },
      response: 'TestSession'
    },
    
    'POST /api/tests/submit': {
      description: '提交测试答案',
      body: { sessionId: 'string', answers: 'TestAnswer[]' },
      response: 'TestResult'
    },
    
    'GET /api/tests/result/[token]': {
      description: '获取分享的测试结果',
      params: { token: 'string' },
      response: 'SharedTestResult'
    }
  },

  // 用户认证API
  auth: {
    'POST /api/auth/register': {
      description: '用户注册',
      body: {
        email: 'string',
        username: 'string',
        password: 'string',
        locale: 'string?'
      },
      response: 'RegisterSuccess'
    },

    'POST /api/auth/login': {
      description: '用户登录',
      body: {
        email: 'string',
        password: 'string',
        rememberMe: 'boolean?'
      },
      response: 'LoginSuccess'
    },

    'POST /api/auth/logout': {
      description: '用户登出',
      auth: 'required',
      response: 'LogoutSuccess'
    },

    'POST /api/auth/refresh': {
      description: '刷新访问令牌',
      body: { refreshToken: 'string' },
      response: 'TokenRefresh'
    },

    'POST /api/auth/forgot-password': {
      description: '忘记密码',
      body: { email: 'string' },
      response: 'PasswordResetSent'
    },

    'POST /api/auth/reset-password': {
      description: '重置密码',
      body: {
        token: 'string',
        newPassword: 'string'
      },
      response: 'PasswordResetSuccess'
    },

    'POST /api/auth/verify-email': {
      description: '验证邮箱',
      body: { token: 'string' },
      response: 'EmailVerified'
    },

    'POST /api/auth/resend-verification': {
      description: '重发验证邮件',
      body: { email: 'string' },
      response: 'VerificationSent'
    }
  },

  // 用户相关API
  user: {
    'GET /api/user/profile': {
      description: '获取用户资料',
      auth: 'required',
      response: 'UserProfile'
    },

    'PUT /api/user/profile': {
      description: '更新用户资料',
      auth: 'required',
      body: {
        username: 'string?',
        avatar: 'string?',
        bio: 'string?'
      },
      response: 'UpdateSuccess'
    },

    'PUT /api/user/preferences': {
      description: '更新用户偏好',
      auth: 'required',
      body: 'UserPreferences',
      response: 'UpdateSuccess'
    },

    'GET /api/user/history': {
      description: '获取用户测试历史',
      auth: 'required',
      params: { page: 'number', limit: 'number' },
      response: 'PaginatedTestResults'
    },

    'GET /api/user/favorites': {
      description: '获取用户收藏文章',
      auth: 'required',
      params: { page: 'number', limit: 'number' },
      response: 'PaginatedBlogPosts'
    },

    'POST /api/user/favorites': {
      description: '收藏文章',
      auth: 'required',
      body: { postId: 'string' },
      response: 'FavoriteAdded'
    },

    'DELETE /api/user/favorites/[postId]': {
      description: '取消收藏文章',
      auth: 'required',
      params: { postId: 'string' },
      response: 'FavoriteRemoved'
    },

    'DELETE /api/user/account': {
      description: '删除用户账户',
      auth: 'required',
      body: { password: 'string' },
      response: 'AccountDeleted'
    }
  },

  // 评论相关API
  comments: {
    'GET /api/comments': {
      description: '获取评论列表',
      params: {
        postId: 'string',
        page: 'number?',
        limit: 'number?',
        parentId: 'string?'
      },
      response: 'PaginatedComments'
    },

    'POST /api/comments': {
      description: '发表评论',
      auth: 'required',
      body: {
        postId: 'string',
        content: 'string',
        parentId: 'string?'
      },
      response: 'CommentCreated'
    },

    'PUT /api/comments/[id]': {
      description: '编辑评论',
      auth: 'required',
      params: { id: 'string' },
      body: { content: 'string' },
      response: 'CommentUpdated'
    },

    'DELETE /api/comments/[id]': {
      description: '删除评论',
      auth: 'required',
      params: { id: 'string' },
      response: 'CommentDeleted'
    }
  }
};
```

### API安全规范
```typescript
// API安全配置
const API_SECURITY = {
  // 认证策略
  authentication: {
    strategy: 'JWT + Refresh Token',
    tokenExpiry: '15 minutes',
    refreshTokenExpiry: '7 days',
    
    middleware: {
      verifyToken: '验证JWT token',
      refreshToken: '刷新过期token',
      optionalAuth: '可选认证（游客模式）'
    }
  },

  // 授权控制
  authorization: {
    roles: ['guest', 'user', 'admin'],
    
    permissions: {
      guest: ['read blog', 'take tests', 'view shared results'],
      user: ['all guest permissions', 'save results', 'comment', 'favorite'],
      admin: ['all user permissions', 'manage content', 'view analytics']
    }
  },

  // 速率限制
  rateLimiting: {
    global: '100 requests per minute',
    
    endpoints: {
      '/api/tests/submit': '5 requests per minute',
      '/api/blog': '60 requests per minute',
      '/api/user/*': '30 requests per minute'
    },
    
    strategy: 'sliding window with Redis'
  },

  // 数据验证
  validation: {
    inputSanitization: 'XSS protection',
    schemaValidation: 'Zod schema validation',
    fileUpload: 'File type and size validation',
    sqlInjection: 'Prisma ORM protection'
  }
};
```

### API响应格式规范
```typescript
// 统一响应格式
const API_RESPONSE_FORMAT = {
  // 成功响应
  success: {
    structure: {
      success: true,
      data: 'T',
      message: 'string?',
      meta: 'ResponseMeta?'
    },
    
    example: {
      success: true,
      data: {
        posts: [...],
        pagination: { page: 1, limit: 10, total: 100 }
      },
      message: 'Posts retrieved successfully'
    }
  },

  // 错误响应
  error: {
    structure: {
      success: false,
      error: {
        code: 'string',
        message: 'string',
        details: 'any?'
      }
    },
    
    errorCodes: {
      VALIDATION_ERROR: '输入验证失败',
      UNAUTHORIZED: '未授权访问',
      FORBIDDEN: '权限不足',
      NOT_FOUND: '资源不存在',
      RATE_LIMITED: '请求频率超限',
      INTERNAL_ERROR: '服务器内部错误'
    }
  },

  // 分页响应
  pagination: {
    structure: {
      page: 'number',
      limit: 'number',
      total: 'number',
      totalPages: 'number',
      hasNext: 'boolean',
      hasPrev: 'boolean'
    }
  }
};
```

## 数据迁移策略

### Cloudflare D1迁移管理
```typescript
// D1数据迁移规范
const D1_MIGRATION_STRATEGY = {
  // 迁移命名规范
  naming: {
    pattern: 'YYYYMMDD_HHMMSS_descriptive_name',
    examples: [
      '20241201_120000_init_d1_database',
      '20241202_090000_add_blog_categories',
      '20241203_150000_convert_arrays_to_json'
    ]
  },

  // D1特定迁移类型
  migrationTypes: {
    schema: 'SQLite数据库结构变更',
    data: '数据迁移和JSON转换',
    index: 'SQLite索引创建和优化',
    jsonConversion: 'PostgreSQL数组到JSON数组的转换',
    cleanup: '数据清理和归档'
  },

  // D1回滚策略
  rollback: {
    timeTravel: '使用D1 Time Travel功能（30天内）',
    manual: '手动回滚指定迁移',
    backup: '使用wrangler d1 export备份',
    testing: '在本地D1环境测试迁移'
  },

  // D1生产环境迁移流程
  productionFlow: {
    steps: [
      '1. 在本地D1环境测试迁移 (wrangler d1 execute --local)',
      '2. 在预发布D1数据库验证',
      '3. 使用wrangler d1 export创建备份',
      '4. 执行远程迁移 (wrangler d1 execute --remote)',
      '5. 验证数据完整性和JSON格式',
      '6. 测试边缘分布和缓存',
      '7. 监控查询性能和错误率'
    ]
  },

  // D1特定注意事项
  d1Considerations: {
    jsonMigration: '将PostgreSQL数组迁移为JSON数组格式',
    indexLimits: 'SQLite索引限制和优化策略',
    edgeSync: '边缘节点数据同步时间',
    queryLimits: '查询限制和批处理策略'
  }
};
```

## API文档规范

### OpenAPI规范
```typescript
// API文档配置
const API_DOCUMENTATION = {
  // OpenAPI配置
  openapi: {
    version: '3.0.0',
    info: {
      title: 'Mystical Website API',
      version: '1.0.0',
      description: '玄学网站API文档',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },

    servers: [
      { url: 'https://api.mystical-website.com', description: 'Production' },
      { url: 'https://staging-api.mystical-website.com', description: 'Staging' },
      { url: 'http://localhost:3000', description: 'Development' }
    ]
  },

  // 文档生成工具
  tools: {
    generator: 'Swagger/OpenAPI Generator',
    ui: 'Swagger UI',
    testing: 'Postman Collection',
    validation: 'API Schema Validation'
  },

  // 文档维护
  maintenance: {
    autoGeneration: '从代码注释自动生成',
    versionControl: 'Git版本控制',
    reviewProcess: 'API变更审查流程',
    deprecation: 'API废弃通知机制'
  }
};
```
