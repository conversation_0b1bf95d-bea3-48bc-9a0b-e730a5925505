'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { BlogPost } from '@/types';
import { BlogCard } from './BlogCard';
import { cn } from '@/lib/utils';

interface BlogListProps {
  posts: BlogPost[];
  featuredPost?: BlogPost;
  className?: string;
  showFeatured?: boolean;
  layout?: 'grid' | 'list';
  columns?: 1 | 2 | 3;
}

export function BlogList({
  posts,
  featuredPost,
  className,
  showFeatured = true,
  layout = 'grid',
  columns = 2,
}: BlogListProps) {
  const t = useTranslations('blog');

  const containerClasses = cn(
    'space-y-8',
    className
  );

  const gridClasses = cn(
    'grid gap-6',
    {
      'grid-cols-1': layout === 'list' || columns === 1,
      'grid-cols-1 md:grid-cols-2': layout === 'grid' && columns === 2,
      'grid-cols-1 md:grid-cols-2 lg:grid-cols-3': layout === 'grid' && columns === 3,
    }
  );

  // 分离特色文章和常规文章
  const regularPosts = featuredPost 
    ? posts.filter(post => post.id !== featuredPost.id)
    : posts;

  return (
    <div className={containerClasses}>
      {/* Featured Post Section */}
      {showFeatured && featuredPost && (
        <section className="mb-12">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-mystical-900 dark:text-white mb-2">
              {t('featuredPost')}
            </h2>
            <p className="text-mystical-600 dark:text-mystical-300">
              {t('featuredPostDescription')}
            </p>
          </div>
          <BlogCard
            post={featuredPost}
            variant="featured"
            className="max-w-4xl mx-auto"
          />
        </section>
      )}

      {/* Regular Posts Section */}
      {regularPosts.length > 0 && (
        <section>
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-mystical-900 dark:text-white mb-2">
              {showFeatured && featuredPost ? t('latestPosts') : t('allPosts')}
            </h2>
            <p className="text-mystical-600 dark:text-mystical-300">
              {t('latestPostsDescription')}
            </p>
          </div>
          
          <div className={gridClasses}>
            {regularPosts.map((post) => (
              <BlogCard
                key={post.id}
                post={post}
                variant={layout === 'list' ? 'compact' : 'default'}
                className={layout === 'list' ? 'flex gap-4' : ''}
              />
            ))}
          </div>
        </section>
      )}

      {/* Empty State */}
      {posts.length === 0 && (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-mystical-100 dark:bg-dark-700 flex items-center justify-center">
              <svg
                className="w-8 h-8 text-mystical-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-mystical-900 dark:text-white mb-2">
              {t('noPosts')}
            </h3>
            <p className="text-mystical-600 dark:text-mystical-300">
              {t('noPostsDescription')}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

// 博客列表骨架屏组件
export function BlogListSkeleton({
  showFeatured = true,
  columns = 2,
}: {
  showFeatured?: boolean;
  columns?: 1 | 2 | 3;
}) {
  const gridClasses = cn(
    'grid gap-6',
    {
      'grid-cols-1': columns === 1,
      'grid-cols-1 md:grid-cols-2': columns === 2,
      'grid-cols-1 md:grid-cols-2 lg:grid-cols-3': columns === 3,
    }
  );

  return (
    <div className="space-y-8 animate-pulse">
      {/* Featured Post Skeleton */}
      {showFeatured && (
        <section className="mb-12">
          <div className="mb-6">
            <div className="h-8 bg-mystical-200 dark:bg-dark-700 rounded w-48 mb-2"></div>
            <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded w-96"></div>
          </div>
          <div className="max-w-4xl mx-auto p-8 bg-mystical-50 dark:bg-dark-800 rounded-xl border border-mystical-200 dark:border-dark-700">
            <div className="aspect-[16/9] bg-mystical-200 dark:bg-dark-700 rounded-xl mb-8"></div>
            <div className="h-4 bg-mystical-200 dark:bg-dark-700 rounded w-24 mb-4"></div>
            <div className="h-8 bg-mystical-200 dark:bg-dark-700 rounded w-3/4 mb-4"></div>
            <div className="space-y-2 mb-6">
              <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded"></div>
              <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded w-5/6"></div>
              <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded w-4/6"></div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-mystical-200 dark:bg-dark-700 rounded-full"></div>
              <div className="h-4 bg-mystical-200 dark:bg-dark-700 rounded w-32"></div>
            </div>
          </div>
        </section>
      )}

      {/* Regular Posts Skeleton */}
      <section>
        <div className="mb-6">
          <div className="h-8 bg-mystical-200 dark:bg-dark-700 rounded w-40 mb-2"></div>
          <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded w-80"></div>
        </div>
        
        <div className={gridClasses}>
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className="p-6 bg-white dark:bg-dark-800 rounded-xl border border-mystical-200 dark:border-dark-700"
            >
              <div className="aspect-[16/9] bg-mystical-200 dark:bg-dark-700 rounded-lg mb-6"></div>
              <div className="h-4 bg-mystical-200 dark:bg-dark-700 rounded w-20 mb-3"></div>
              <div className="h-6 bg-mystical-200 dark:bg-dark-700 rounded w-4/5 mb-3"></div>
              <div className="space-y-2 mb-4">
                <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded"></div>
                <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-5/6"></div>
              </div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-6 h-6 bg-mystical-200 dark:bg-dark-700 rounded-full"></div>
                <div className="h-3 bg-mystical-200 dark:bg-dark-700 rounded w-24"></div>
              </div>
              <div className="flex justify-between">
                <div className="flex gap-4">
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-16"></div>
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-12"></div>
                </div>
                <div className="flex gap-3">
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-8"></div>
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-8"></div>
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-8"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
