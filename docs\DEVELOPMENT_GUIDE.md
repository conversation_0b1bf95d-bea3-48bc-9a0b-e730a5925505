# 玄学多语言网站开发指南

## 📋 项目概述

这是一个专业的多语言玄学网站项目，包含塔罗、星座、数字命理等东西方神秘学内容。采用内容驱动的SEO策略，最终发展为权威垂直网站并提供高质量外链服务。

### 🎯 核心目标
- **SEO至上**：每个页面完整SEO配置，目标DA 60+
- **多语言支持**：6种核心语言，支持RTL布局
- **移动端优先**：响应式设计，优秀的移动体验
- **内容+测试一体化**：博客内容与在线测试无缝结合

### 🛠 技术栈
- **前端**：Next.js 14 + TypeScript + Tailwind CSS
- **后端**：Next.js API Routes + Prisma ORM
- **数据库**：Supabase PostgreSQL + Upstash Redis
- **部署**：Vercel + GitHub Actions
- **AI集成**：通义千问 + 豆包 + 智谱AI

## 🚀 开发策略

### 阶段一：基础架构搭建（第1-2周）

#### 第1步：项目初始化
**引用规则**：`00-master-rules.md` + `05-deployment-monitoring-rules.md`

**开发提示词**：
``` 
我要初始化玄学多语言网站项目，请基于以下rules进行开发：
- 主要规则：00-master-rules.md（项目总纲和技术栈）
- 辅助规则：05-deployment-monitoring-rules.md（部署配置）
- 特别注意：
  1. 使用Next.js 14 App Router
  2. 配置TypeScript严格模式
  3. 设置Tailwind CSS神秘主题色彩
  4. 配置Vercel部署和GitHub集成
  5. 设置环境变量管理

请创建完整的项目结构，包括package.json、tsconfig.json、tailwind.config.js等配置文件。
```

**任务清单**：
- [x] 创建Next.js 14项目
- [x] 配置TypeScript、ESLint、Prettier
- [x] 设置Tailwind CSS神秘主题
- [x] 配置GitHub仓库
- [x] 设置Vercel部署
- [x] 配置环境变量

#### 第2步：设计系统实现
**引用规则**：`01-frontend-design-rules.md`

**开发提示词**：
```
我要实现设计系统基础组件，请基于以下rules进行开发：
- 主要规则：01-frontend-design-rules.md（完整设计系统）
- 特别注意：
  1. 实现神秘紫色+黄金色配色系统
  2. 配置4种字体族（sans、serif、mystical、mono）
  3. 创建原子组件（Button、Input、Typography等）
  4. 实现深色模式支持
  5. 配置响应式断点系统

请严格按照设计规范创建组件，确保颜色、字体、间距完全一致。
```

**任务清单**：
- [x] 实现颜色系统和字体系统
- [x] 创建Button组件（多种变体）
- [x] 创建Typography组件
- [x] 创建Input和Form组件
- [x] 实现深色模式切换
- [x] 配置响应式断点

### 阶段二：核心功能开发（第3-6周）

#### 第3步：多语言架构
**引用规则**：`00-master-rules.md` + `06-mobile-multilingual-rules.md`

**开发提示词**：
```
我要实现多语言架构，请基于以下rules进行开发：
- 主要规则：06-mobile-multilingual-rules.md（多语言规范）
- 辅助规则：00-master-rules.md（多语言策略）
- 特别注意：
  1. 配置next-intl支持6种语言（en、zh、es、pt、hi、ja）
  2. 实现SEO友好的URL结构：/[locale]/[category]/[slug]
  3. 配置hreflang标签
  4. 实现语言切换组件
  5. 支持RTL语言布局
  6. 实现文化敏感的设计适配

请确保每种语言都有独立的SEO元数据和内容管理。
```

**任务清单**：
- [x] 配置next-intl
- [x] 创建语言文件（6种语言）
- [x] 实现语言切换组件
- [x] 配置SEO友好URL
- [x] 实现RTL布局支持
- [x] 创建多语言导航

#### 第4步：博客系统开发
**引用规则**：`03-blog-management-rules.md` + `01-frontend-design-rules.md`

**开发提示词**：
```
我要开发博客系统，请基于以下rules进行开发：
- 主要规则：03-blog-management-rules.md（博客管理系统）
- 辅助规则：01-frontend-design-rules.md（博客页面设计）
- 特别注意：
  1. 实现对标Medium的博客文章页面设计
  2. 680px最佳阅读宽度，1.75倍行高
  3. 完整的博客列表页面（特色文章+常规列表）
  4. 阅读体验优化（进度指示、目录导航、互动功能）
  5. AI内容导入系统
  6. 测试导流内容策略

请严格按照设计规范实现，确保阅读体验达到Medium水准。
```

**任务清单**：
- [x] 实现博客文章页面（完整设计）
- [x] 实现博客列表页面
- [x] 创建阅读进度指示器
- [x] 实现目录导航
- [x] 创建评论系统
- [x] 实现内容管理后台

#### 第5步：测试功能开发
**引用规则**：`04-online-tests-rules.md` + `02-component-architecture-rules.md`

**开发提示词**：
```
我要开发在线测试功能，请基于以下rules进行开发：
- 主要规则：04-online-tests-rules.md（测试功能规范）
- 辅助规则：02-component-architecture-rules.md（玄学组件）
- 特别注意：
  1. 实现6种测试类型（塔罗、星座、数字命理、水晶、手相、梦境）
  2. 创建玄学专用组件（塔罗牌、星座轮盘等）
  3. 集成AI分析功能（通义千问等）
  4. 实现测试结果分享系统
  5. 优化测试流程用户体验

请确保每个测试都有独特的视觉设计和交互体验。
```

**任务清单**：
- [ ] 创建塔罗牌组件系统
- [ ] 实现星座轮盘组件
- [ ] 开发数字命理计算器
- [ ] 集成AI分析API
- [ ] 实现测试结果分享
- [ ] 创建测试历史管理

### 阶段三：高级功能和优化（第7-10周）

#### 第6步：移动端优化
**引用规则**：`06-mobile-multilingual-rules.md` + `01-frontend-design-rules.md`

**开发提示词**：
```
我要优化移动端体验，请基于以下rules进行开发：
- 主要规则：06-mobile-multilingual-rules.md（移动端规范）
- 辅助规则：01-frontend-design-rules.md（响应式设计）
- 特别注意：
  1. 移动优先的设计策略
  2. 触摸友好的交互设计
  3. 移动端专用导航
  4. 性能优化和加载速度
  5. 移动端测试体验优化

请确保移动端体验不逊于桌面端。
```

#### 第7步：SEO和内容优化
**引用规则**：`00-master-rules.md` + `03-blog-management-rules.md`

**开发提示词**：
```
我要实现SEO优化，请基于以下rules进行开发：
- 主要规则：00-master-rules.md（SEO至上原则）
- 辅助规则：03-blog-management-rules.md（SEO内容策略）
- 特别注意：
  1. 动态SEO元数据生成
  2. 结构化数据(JSON-LD)
  3. 站点地图和robots.txt
  4. 内部链接策略
  5. 核心网络指标优化

目标是达到DA 60+的权威网站水准。
```

#### 第8步：监控和部署优化
**引用规则**：`05-deployment-monitoring-rules.md`

**开发提示词**：
```
我要配置监控和部署优化，请基于以下rules进行开发：
- 主要规则：05-deployment-monitoring-rules.md（部署监控规范）
- 特别注意：
  1. 性能监控配置
  2. 错误监控和日志
  3. 自动化部署流程
  4. 备份和恢复策略
  5. 分析工具集成

请确保生产环境的稳定性和可观测性。
```

## 📝 开发规范

### Rules引用策略

#### 1. 每次开发前必读
- `00-master-rules.md` - 确保符合项目总体方向

#### 2. 按功能模块引用
- **UI组件开发** → `01-frontend-design-rules.md` + `02-component-architecture-rules.md`
- **博客功能** → `03-blog-management-rules.md` + `01-frontend-design-rules.md`
- **测试功能** → `04-online-tests-rules.md` + `02-component-architecture-rules.md`
- **移动端开发** → `06-mobile-multilingual-rules.md` + `01-frontend-design-rules.md`
- **数据库API** → `07-database-api-rules.md`
- **部署相关** → `05-deployment-monitoring-rules.md`

#### 3. 开发提示词模板
```
我要开发[具体功能]，请基于以下rules进行开发：
- 主要规则：[主要规则文件]
- 辅助规则：[辅助规则文件]
- 特别注意：[特定要求]

请确保严格遵循设计系统和架构规范。
```

### 代码质量标准

#### TypeScript配置
- 启用严格模式
- 100%类型覆盖
- 禁用any类型

#### 组件开发规范
- 遵循原子设计原则
- 单一职责原则
- 完整的Props类型定义
- 90%+测试覆盖率

#### 性能要求
- Core Web Vitals优化
- 图片懒加载
- 代码分割
- 缓存策略

## 🔧 开发工具

### 必需工具
- **IDE**：VSCode + 推荐插件
- **包管理**：npm/yarn
- **版本控制**：Git + GitHub
- **部署**：Vercel CLI
- **数据库**：Prisma Studio

### 推荐插件
- Tailwind CSS IntelliSense
- Prisma
- ESLint
- Prettier
- Auto Rename Tag
- GitLens

## 📊 项目里程碑

### 第1个月：基础架构
- ✅ 项目初始化
- ✅ 设计系统
- ✅ 多语言架构
- ✅ 基础组件库

### 第2个月：核心功能
- 🔄 博客系统
- 🔄 测试功能
- 🔄 用户系统
- 🔄 内容管理

### 第3个月：优化完善
- ⏳ 移动端优化
- ⏳ SEO优化
- ⏳ 性能优化
- ⏳ 监控部署

### 第4个月：上线运营
- ⏳ 内容导入
- ⏳ 用户测试
- ⏳ 性能调优
- ⏳ 正式发布

## 🚨 注意事项

### 开发原则
1. **严格遵循Rules**：每个功能开发前必须引用相应规则
2. **设计一致性**：确保所有组件遵循设计系统
3. **性能优先**：每个功能都要考虑性能影响
4. **SEO友好**：所有页面都要有完整SEO配置
5. **多语言支持**：确保所有功能支持多语言

### 常见陷阱
- ❌ 不要偏离设计系统
- ❌ 不要忽略移动端体验
- ❌ 不要忘记SEO配置
- ❌ 不要硬编码文本内容
- ❌ 不要忽略性能优化

### 质量检查
- 代码审查必须通过
- 测试覆盖率达标
- 性能指标合格
- 设计规范一致
- 多语言完整

---

**记住**：这个项目的核心目标是通过高质量内容建立SEO权威性，最终发展为可持续的外链服务业务。每个技术决策都应该服务于这个商业目标。
