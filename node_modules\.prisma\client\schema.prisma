// Prisma Schema for Mystical Website
// 玄学网站数据库模型定义

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  avatar    String?
  locale    String   @default("en")
  theme     String   @default("light")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  testResults   TestResult[]
  blogViews     BlogView[]
  comments      Comment[]
  favorites     UserFavorite[]
  sessions      UserSession[]
  verifications UserVerification[]

  @@map("users")
}

// 博客文章表（优化的纯数据库存储版本）
model BlogPost {
  id          String     @id @default(cuid())
  title       String     @db.VarChar(200)
  slug        String     @unique @db.VarChar(250)
  content     String     @db.Text // 支持富文本HTML内容
  excerpt     String?    @db.VarChar(500)
  coverImage  String?    @db.VarChar(500)
  locale      String     @db.VarChar(10)
  category    String     @db.VarChar(50)
  tags        String[] // PostgreSQL数组类型
  status      PostStatus @default(DRAFT)
  publishedAt DateTime?
  viewCount   Int        @default(0)
  readingTime Int        @default(0) // 分钟数

  // SEO优化字段
  seoTitle       String?  @db.VarChar(60)
  seoDescription String?  @db.VarChar(160)
  keywords       String[] // SEO关键词数组

  // 扩展元数据（JSON格式，存储AI生成信息等）
  metadata Json? @db.JsonB

  // 时间戳
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  views     BlogView[]
  comments  Comment[]
  favorites UserFavorite[]

  // 复合索引优化
  @@index([locale, category])
  @@index([status, publishedAt])
  @@index([locale, status, publishedAt])
  @@map("blog_posts")
}

// 测试结果表
model TestResult {
  id         String   @id @default(cuid())
  userId     String?
  testType   TestType
  answers    Json
  result     Json
  shareToken String?  @unique
  isPublic   Boolean  @default(false)
  createdAt  DateTime @default(now())

  // 关联关系
  user User? @relation(fields: [userId], references: [id])

  @@map("test_results")
}

// 枚举类型
enum PostStatus {
  DRAFT
  PENDING
  SCHEDULED
  PUBLISHED
  ARCHIVED
  DELETED
}

enum TestType {
  TAROT
  ASTROLOGY
  NUMEROLOGY
  CRYSTAL
  PALMISTRY
  DREAMS
}

// 评论表
model Comment {
  id         String   @id @default(cuid())
  content    String   @db.Text
  userId     String?
  postId     String
  parentId   String? // 回复评论的父评论ID
  isApproved Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // 关联关系
  user    User?     @relation(fields: [userId], references: [id])
  post    BlogPost  @relation(fields: [postId], references: [id], onDelete: Cascade)
  parent  Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies Comment[] @relation("CommentReplies")

  @@map("comments")
}

// 用户收藏表
model UserFavorite {
  id        String   @id @default(cuid())
  userId    String
  postId    String
  createdAt DateTime @default(now())

  // 关联关系
  user User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  post BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)

  // 唯一约束：用户不能重复收藏同一篇文章
  @@unique([userId, postId])
  @@map("user_favorites")
}

// 博客浏览记录表
model BlogView {
  id        String   @id @default(cuid())
  postId    String
  userId    String? // 可选，游客访问时为null
  ipAddress String? // 用于统计唯一访问
  userAgent String?
  createdAt DateTime @default(now())

  // 关联关系
  post BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  user User?    @relation(fields: [userId], references: [id])

  @@map("blog_views")
}

// 用户会话表
model UserSession {
  id           String   @id @default(cuid())
  userId       String
  sessionToken String   @unique
  refreshToken String   @unique
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// 用户验证表（邮箱验证、密码重置等）
model UserVerification {
  id        String           @id @default(cuid())
  userId    String?
  email     String?
  token     String           @unique
  type      VerificationType
  expiresAt DateTime
  used      Boolean          @default(false)
  createdAt DateTime         @default(now())

  // 关联关系
  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_verifications")
}

// 验证类型枚举
enum VerificationType {
  EMAIL_VERIFICATION
  PASSWORD_RESET
  ACCOUNT_DELETION
}
