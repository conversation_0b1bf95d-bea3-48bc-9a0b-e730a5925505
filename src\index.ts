// Cloudflare Workers入口文件
// Cloudflare Workers entry point

import { createDatabaseClient, type D1Database, type Env } from './lib/d1'

// Workers环境接口
export interface WorkerEnv extends Env {
  DB: D1Database
  CACHE: KVNamespace
  ASSETS: R2Bucket
  
  // 环境变量
  ENVIRONMENT: string
  NEXT_PUBLIC_APP_NAME: string
  NEXT_PUBLIC_APP_URL: string
  DEBUG?: string
  
  // AI服务配置
  QWEN_API_KEY?: string
  QWEN_API_URL?: string
  DOUBAO_API_KEY?: string
  DOUBAO_API_URL?: string
  ZHIPU_API_KEY?: string
  ZHIPU_API_URL?: string
  
  // 监控配置
  SENTRY_DSN?: string
  NEXT_PUBLIC_UMAMI_WEBSITE_ID?: string
  
  // 邮件配置
  SMTP_HOST?: string
  SMTP_PORT?: string
  SMTP_USER?: string
  SMTP_PASS?: string
}

// 主要的Worker处理函数
export default {
  async fetch(request: Request, env: WorkerEnv, ctx: ExecutionContext): Promise<Response> {
    try {
      // 创建数据库客户端
      const db = createDatabaseClient(env)
      
      // 解析请求URL
      const url = new URL(request.url)
      const { pathname, searchParams } = url
      
      // 设置CORS头
      const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
      
      // 处理OPTIONS请求（CORS预检）
      if (request.method === 'OPTIONS') {
        return new Response(null, { headers: corsHeaders })
      }
      
      // API路由处理
      if (pathname.startsWith('/api/')) {
        return handleApiRequest(request, env, db, pathname)
      }
      
      // 健康检查
      if (pathname === '/health') {
        return new Response(JSON.stringify({
          status: 'healthy',
          environment: env.ENVIRONMENT,
          timestamp: new Date().toISOString(),
          database: 'D1 connected'
        }), {
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        })
      }
      
      // 静态资源处理
      if (pathname.startsWith('/assets/') || pathname.startsWith('/images/')) {
        return handleStaticAssets(request, env.ASSETS, pathname)
      }
      
      // 默认返回404
      return new Response('Not Found', { 
        status: 404,
        headers: corsHeaders
      })
      
    } catch (error) {
      console.error('Worker error:', error)
      
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      })
    }
  },
}

// API请求处理函数
async function handleApiRequest(
  request: Request, 
  env: WorkerEnv, 
  db: any, 
  pathname: string
): Promise<Response> {
  const method = request.method
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Content-Type': 'application/json'
  }
  
  try {
    // 博客API
    if (pathname.startsWith('/api/blog')) {
      return handleBlogApi(request, env, db, pathname, method)
    }
    
    // 测试API
    if (pathname.startsWith('/api/tests')) {
      return handleTestsApi(request, env, db, pathname, method)
    }
    
    // AI API
    if (pathname.startsWith('/api/ai')) {
      return handleAiApi(request, env, pathname, method)
    }
    
    // 用户API
    if (pathname.startsWith('/api/users')) {
      return handleUsersApi(request, env, db, pathname, method)
    }
    
    return new Response(JSON.stringify({ error: 'API endpoint not found' }), {
      status: 404,
      headers: corsHeaders
    })
    
  } catch (error) {
    console.error('API error:', error)
    return new Response(JSON.stringify({
      error: 'API Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: corsHeaders
    })
  }
}

// 博客API处理
async function handleBlogApi(
  request: Request,
  env: WorkerEnv,
  db: any,
  pathname: string,
  method: string
): Promise<Response> {
  // 导入博客相关的D1操作
  const { getBlogPostsD1, getBlogPostBySlugD1, createBlogPostD1 } = await import('./lib/database-d1')
  
  const corsHeaders = { 'Access-Control-Allow-Origin': '*', 'Content-Type': 'application/json' }
  
  if (method === 'GET') {
    // 获取博客列表或单篇文章
    const url = new URL(request.url)
    const slug = url.searchParams.get('slug')
    const locale = url.searchParams.get('locale') || 'en'
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    
    if (slug) {
      // 获取单篇文章
      const post = await getBlogPostBySlugD1(env.DB, slug, locale)
      if (!post) {
        return new Response(JSON.stringify({ error: 'Post not found' }), {
          status: 404,
          headers: corsHeaders
        })
      }
      return new Response(JSON.stringify(post), { headers: corsHeaders })
    } else {
      // 获取文章列表
      const result = await getBlogPostsD1(env.DB, { locale, page, limit })
      return new Response(JSON.stringify(result), { headers: corsHeaders })
    }
  }
  
  if (method === 'POST') {
    // 创建新文章
    const data = await request.json()
    const result = await createBlogPostD1(env.DB, data)
    return new Response(JSON.stringify(result), { 
      status: 201,
      headers: corsHeaders 
    })
  }
  
  return new Response(JSON.stringify({ error: 'Method not allowed' }), {
    status: 405,
    headers: corsHeaders
  })
}

// 测试API处理
async function handleTestsApi(
  request: Request,
  env: WorkerEnv,
  db: any,
  pathname: string,
  method: string
): Promise<Response> {
  const { createTestResultD1, getTestResultD1 } = await import('./lib/database-d1')
  const corsHeaders = { 'Access-Control-Allow-Origin': '*', 'Content-Type': 'application/json' }
  
  if (method === 'POST') {
    const data = await request.json()
    const result = await createTestResultD1(env.DB, data)
    return new Response(JSON.stringify(result), { 
      status: 201,
      headers: corsHeaders 
    })
  }
  
  if (method === 'GET') {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')
    if (id) {
      const result = await getTestResultD1(env.DB, id)
      return new Response(JSON.stringify(result), { headers: corsHeaders })
    }
  }
  
  return new Response(JSON.stringify({ error: 'Method not allowed' }), {
    status: 405,
    headers: corsHeaders
  })
}

// AI API处理
async function handleAiApi(
  request: Request,
  env: WorkerEnv,
  pathname: string,
  method: string
): Promise<Response> {
  const corsHeaders = { 'Access-Control-Allow-Origin': '*', 'Content-Type': 'application/json' }
  
  if (method === 'POST') {
    const data = await request.json()
    const { prompt, model = 'qwen' } = data
    
    // 根据模型选择API
    let apiKey: string | undefined
    let apiUrl: string | undefined
    
    switch (model) {
      case 'qwen':
        apiKey = env.QWEN_API_KEY
        apiUrl = env.QWEN_API_URL
        break
      case 'doubao':
        apiKey = env.DOUBAO_API_KEY
        apiUrl = env.DOUBAO_API_URL
        break
      case 'zhipu':
        apiKey = env.ZHIPU_API_KEY
        apiUrl = env.ZHIPU_API_URL
        break
      default:
        return new Response(JSON.stringify({ error: 'Unsupported model' }), {
          status: 400,
          headers: corsHeaders
        })
    }
    
    if (!apiKey || !apiUrl) {
      return new Response(JSON.stringify({ error: 'AI service not configured' }), {
        status: 503,
        headers: corsHeaders
      })
    }
    
    // 调用AI API（这里需要根据具体的AI服务实现）
    // 这是一个简化的示例
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ prompt })
    })
    
    const result = await response.json()
    return new Response(JSON.stringify(result), { headers: corsHeaders })
  }
  
  return new Response(JSON.stringify({ error: 'Method not allowed' }), {
    status: 405,
    headers: corsHeaders
  })
}

// 用户API处理
async function handleUsersApi(
  request: Request,
  env: WorkerEnv,
  db: any,
  pathname: string,
  method: string
): Promise<Response> {
  const corsHeaders = { 'Access-Control-Allow-Origin': '*', 'Content-Type': 'application/json' }
  
  // 用户相关API实现
  return new Response(JSON.stringify({ message: 'Users API not implemented yet' }), {
    headers: corsHeaders
  })
}

// 静态资源处理
async function handleStaticAssets(
  request: Request,
  bucket: R2Bucket,
  pathname: string
): Promise<Response> {
  try {
    const object = await bucket.get(pathname.substring(1)) // 移除开头的 '/'
    
    if (!object) {
      return new Response('Asset not found', { status: 404 })
    }
    
    const headers = new Headers()
    object.writeHttpMetadata(headers)
    headers.set('etag', object.httpEtag)
    headers.set('cache-control', 'public, max-age=31536000') // 1年缓存
    
    return new Response(object.body, { headers })
    
  } catch (error) {
    console.error('Static asset error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}
