// Cloudflare D1数据库连接和工具函数
// Cloudflare D1 database connection and utility functions

import { PrismaClient } from '@prisma/client'
import { PrismaD1 } from '@prisma/adapter-d1'

// D1数据库类型定义
export interface D1Database {
  prepare(query: string): D1PreparedStatement
  dump(): Promise<ArrayBuffer>
  batch<T = unknown>(statements: D1PreparedStatement[]): Promise<D1Result<T>[]>
  exec(query: string): Promise<D1ExecResult>
}

export interface D1PreparedStatement {
  bind(...values: unknown[]): D1PreparedStatement
  first<T = unknown>(colName?: string): Promise<T | null>
  run(): Promise<D1Result>
  all<T = unknown>(): Promise<D1Result<T>>
  raw<T = unknown>(): Promise<T[]>
}

export interface D1Result<T = Record<string, unknown>> {
  results?: T[]
  success: boolean
  error?: string
  meta: {
    duration: number
    size_after: number
    rows_read: number
    rows_written: number
  }
}

export interface D1ExecResult {
  count: number
  duration: number
}

// 环境变量类型定义
export interface Env {
  DB: D1Database
  [key: string]: unknown
}

// 创建D1适配的Prisma客户端
export function createPrismaClient(db: D1Database) {
  const adapter = new PrismaD1(db)
  return new PrismaClient({ 
    adapter,
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  })
}

// D1数据库连接测试函数
export async function testD1Connection(db: D1Database): Promise<boolean> {
  try {
    const result = await db.prepare('SELECT 1 as health').first()
    console.log('✅ D1数据库连接成功 / D1 Database connected successfully')
    return true
  } catch (error) {
    console.error('❌ D1数据库连接失败 / D1 Database connection failed:', error)
    return false
  }
}

// D1数据库健康检查
export async function checkD1Health(db: D1Database) {
  try {
    const result = await db.prepare('SELECT 1 as health, datetime("now") as timestamp').first()
    return { healthy: true, result }
  } catch (error) {
    return { 
      healthy: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

// JSON数据处理工具函数
export class D1JsonUtils {
  // 将JavaScript数组转换为JSON字符串存储
  static arrayToJson(array: string[]): string {
    return JSON.stringify(array)
  }

  // 将JSON字符串转换为JavaScript数组
  static jsonToArray(jsonString: string): string[] {
    try {
      return JSON.parse(jsonString)
    } catch {
      return []
    }
  }

  // 将JavaScript对象转换为JSON字符串存储
  static objectToJson(obj: Record<string, unknown>): string {
    return JSON.stringify(obj)
  }

  // 将JSON字符串转换为JavaScript对象
  static jsonToObject(jsonString: string): Record<string, unknown> {
    try {
      return JSON.parse(jsonString)
    } catch {
      return {}
    }
  }

  // 使用D1 JSON函数查询数组中的值
  static createArraySearchQuery(column: string, searchValue: string): string {
    return `json_extract(${column}, '$') LIKE '%"${searchValue}"%'`
  }

  // 使用D1 JSON函数提取数组长度
  static createArrayLengthQuery(column: string): string {
    return `json_array_length(${column})`
  }

  // 使用D1 JSON函数提取对象属性
  static createObjectPropertyQuery(column: string, property: string): string {
    return `json_extract(${column}, '$.${property}')`
  }
}

// D1批量操作工具
export class D1BatchUtils {
  // 批量插入博客文章
  static async batchInsertBlogPosts(
    db: D1Database, 
    posts: Array<{
      id: string
      title: string
      slug: string
      content: string
      locale: string
      category: string
      tags: string[]
      keywords: string[]
      metadata?: Record<string, unknown>
    }>
  ) {
    const statements = posts.map(post => 
      db.prepare(`
        INSERT INTO blog_posts (
          id, title, slug, content, locale, category, 
          tags, keywords, metadata, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `).bind(
        post.id,
        post.title,
        post.slug,
        post.content,
        post.locale,
        post.category,
        D1JsonUtils.arrayToJson(post.tags),
        D1JsonUtils.arrayToJson(post.keywords),
        post.metadata ? D1JsonUtils.objectToJson(post.metadata) : null
      )
    )

    return await db.batch(statements)
  }

  // 批量更新文章标签
  static async batchUpdateTags(
    db: D1Database,
    updates: Array<{ id: string; tags: string[] }>
  ) {
    const statements = updates.map(update =>
      db.prepare(`
        UPDATE blog_posts 
        SET tags = ?, updatedAt = datetime('now')
        WHERE id = ?
      `).bind(
        D1JsonUtils.arrayToJson(update.tags),
        update.id
      )
    )

    return await db.batch(statements)
  }
}

// D1查询构建器
export class D1QueryBuilder {
  // 构建带标签搜索的博客查询
  static buildBlogSearchQuery(filters: {
    locale?: string
    category?: string
    tags?: string[]
    status?: string
    limit?: number
    offset?: number
  }) {
    let query = `
      SELECT 
        id, title, slug, excerpt, coverImage, locale, category,
        tags, keywords, status, publishedAt, viewCount, readingTime,
        createdAt, updatedAt
      FROM blog_posts 
      WHERE 1=1
    `
    const bindings: unknown[] = []

    if (filters.locale) {
      query += ` AND locale = ?`
      bindings.push(filters.locale)
    }

    if (filters.category) {
      query += ` AND category = ?`
      bindings.push(filters.category)
    }

    if (filters.status) {
      query += ` AND status = ?`
      bindings.push(filters.status)
    }

    if (filters.tags && filters.tags.length > 0) {
      const tagConditions = filters.tags.map(() => 
        `json_extract(tags, '$') LIKE ?`
      ).join(' OR ')
      query += ` AND (${tagConditions})`
      filters.tags.forEach(tag => {
        bindings.push(`%"${tag}"%`)
      })
    }

    query += ` ORDER BY publishedAt DESC`

    if (filters.limit) {
      query += ` LIMIT ?`
      bindings.push(filters.limit)
    }

    if (filters.offset) {
      query += ` OFFSET ?`
      bindings.push(filters.offset)
    }

    return { query, bindings }
  }

  // 构建测试结果查询
  static buildTestResultQuery(filters: {
    userId?: string
    testType?: string
    isPublic?: boolean
    limit?: number
  }) {
    let query = `
      SELECT 
        id, userId, testType, answers, result, shareToken, 
        isPublic, createdAt
      FROM test_results 
      WHERE 1=1
    `
    const bindings: unknown[] = []

    if (filters.userId) {
      query += ` AND userId = ?`
      bindings.push(filters.userId)
    }

    if (filters.testType) {
      query += ` AND testType = ?`
      bindings.push(filters.testType)
    }

    if (filters.isPublic !== undefined) {
      query += ` AND isPublic = ?`
      bindings.push(filters.isPublic)
    }

    query += ` ORDER BY createdAt DESC`

    if (filters.limit) {
      query += ` LIMIT ?`
      bindings.push(filters.limit)
    }

    return { query, bindings }
  }
}

// 导出类型
export type { 
  User, 
  BlogPost, 
  TestResult, 
  Comment, 
  UserFavorite, 
  BlogView, 
  UserSession, 
  UserVerification,
  PostStatus,
  TestType,
  VerificationType
} from '@prisma/client'
