---
type: "conditional_apply"
description: "数据库设计和API架构规范"
---

# 数据库设计和API架构规范

## 数据库架构设计

### 核心设计原则

1. **Cloudflare D1优化**：基于SQLite的边缘数据库，全球分布式存储
2. **JSON数据存储**：利用D1原生JSON函数处理复杂数据结构
3. **多语言支持**：通过locale字段和JSON存储实现多语言内容
4. **SEO友好**：优化的索引策略和URL结构
5. **性能优先**：合理的索引设计和查询优化

### 数据库模型概览

**核心表结构**：
- `User` - 用户基础信息和偏好设置
- `BlogPost` - 博客文章（支持多语言和SEO优化）
- `TestResult` - 测试结果（支持分享和历史记录）
- `Comment` - 评论系统（支持嵌套回复）
- `UserFavorite` - 用户收藏
- `BlogView` - 浏览统计
- `UserSession` - 用户会话管理
- `UserVerification` - 邮箱验证和密码重置

**详细模型定义请参考**：`prisma/schema.prisma`

### D1数据库优化策略

#### 索引设计
```sql
-- 博客文章核心索引
CREATE INDEX idx_blog_posts_locale_category ON blog_posts(locale, category);
CREATE INDEX idx_blog_posts_status_published ON blog_posts(status, publishedAt);
CREATE INDEX idx_blog_posts_slug ON blog_posts(slug);

-- 测试结果索引
CREATE INDEX idx_test_results_user_type ON test_results(userId, testType);
CREATE INDEX idx_test_results_share_token ON test_results(shareToken);

-- 用户相关索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_comments_post_created ON comments(postId, createdAt);
```

#### JSON数据查询示例
```sql
-- 标签搜索
SELECT * FROM blog_posts
WHERE json_extract(tags, '$') LIKE '%"react"%'
AND status = 'PUBLISHED';

-- 关键词提取
SELECT id, title,
       json_extract(keywords, '$[0]') as primary_keyword,
       json_array_length(keywords) as keyword_count
FROM blog_posts WHERE locale = ?;

-- AI生成内容查询
SELECT * FROM blog_posts
WHERE json_extract(metadata, '$.aiGenerated') = true;
```

#### 缓存策略
- **博客文章**：1小时TTL，按slug/category/locale缓存
- **测试结果**：24小时TTL，按shareToken缓存
- **用户偏好**：6小时TTL，按userId缓存
- **边缘缓存**：利用Cloudflare Cache API和Worker KV

#### 性能限制
- **查询限制**：免费版50次/调用，付费版1000次/调用
- **连接限制**：最大6个并发连接
- **批量操作**：使用D1 batch API优化性能

## API设计规范

### 核心API端点

#### 博客API
```typescript
// 核心博客API
const BLOG_API = {
  'GET /api/blog': '获取文章列表（支持分页、分类、搜索）',
  'GET /api/blog/[slug]': '获取文章详情',
  'POST /api/blog': '创建文章（支持AI生成）',
  'PUT /api/blog/[id]': '更新文章',
  'POST /api/blog/[id]/view': '记录浏览量',
  'POST /api/blog/ai-generate': 'AI生成文章内容'
};
```

#### 测试API
```typescript
// 玄学测试API
const TEST_API = {
  'POST /api/tests/start': '开始新测试',
  'POST /api/tests/submit': '提交测试答案',
  'GET /api/tests/result/[token]': '获取分享结果'
};
```

#### 用户API
```typescript
// 用户认证和管理API
const USER_API = {
  // 认证
  'POST /api/auth/register': '用户注册',
  'POST /api/auth/login': '用户登录',
  'POST /api/auth/logout': '用户登出',

  // 用户管理
  'GET /api/user/profile': '获取用户资料',
  'PUT /api/user/profile': '更新用户资料',
  'GET /api/user/favorites': '获取收藏文章',
  'POST /api/user/favorites': '收藏文章'
};
```

### API安全和响应规范

#### 认证授权
- **认证策略**：JWT + Refresh Token
- **用户角色**：guest（游客）、user（用户）、admin（管理员）
- **权限控制**：基于角色的访问控制（RBAC）

#### 速率限制
- **全局限制**：100请求/分钟
- **测试提交**：5请求/分钟
- **博客API**：60请求/分钟
- **用户API**：30请求/分钟

#### 数据验证
- **输入验证**：Zod schema validation
- **XSS防护**：输入清理和转义
- **SQL注入防护**：Prisma ORM自动防护

#### 统一响应格式
```typescript
// 成功响应
{
  success: true,
  data: T,
  message?: string,
  meta?: { pagination: { page, limit, total, hasNext, hasPrev } }
}

// 错误响应
{
  success: false,
  error: {
    code: 'VALIDATION_ERROR' | 'UNAUTHORIZED' | 'NOT_FOUND' | 'RATE_LIMITED',
    message: string,
    details?: any
  }
}
```

## 数据库管理和部署

### 迁移管理
- **命名规范**：`YYYYMMDD_HHMMSS_descriptive_name`
- **本地测试**：`wrangler d1 execute --local`
- **生产部署**：`wrangler d1 execute --remote`
- **备份策略**：`wrangler d1 export`
- **回滚机制**：D1 Time Travel（30天内）

### 开发工作流
```bash
# 1. 修改schema
vim prisma/schema.prisma

# 2. 生成迁移
npx prisma migrate dev --name add_new_feature

# 3. 本地测试
npm run d1:migrate:local

# 4. 生产部署
npm run d1:migrate:remote
```

## 相关文档

### 详细设置指南
- **D1数据库设置**：`docs/cloudflare-d1-setup.md`
- **环境变量配置**：`docs/ENV_SETUP.md`
- **数据库模型定义**：`prisma/schema.prisma`

### 开发参考
- **API开发提示词**：`docs/DEVELOPMENT_PROMPTS.md`
- **用户系统规范**：`.augment/rules/08-user-system-rules.md`
- **环境变量规范**：`.augment/rules/09-environment-variables-rules.md`
