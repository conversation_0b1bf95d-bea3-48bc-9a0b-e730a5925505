name = "tarot-seo"
main = "src/index.ts"
compatibility_date = "2023-12-01"

# 全局环境变量配置
[vars]
ENVIRONMENT = "development"
NEXT_PUBLIC_APP_NAME = "Mystical Website"
NEXT_PUBLIC_APP_URL = "http://localhost:3000"

# D1数据库配置
# 统一数据库：开发和生产环境共用
[[d1_databases]]
binding = "DB" # 在Worker中通过env.DB访问
database_name = "tarot-seo-db"
database_id = "87945106-1e0d-4375-967c-d89324987198"
location = "enam" # 东北美地区，全球连接性最佳

# KV存储配置（用于缓存）
[[kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"

# R2存储配置（用于文件存储）
[[r2_buckets]]
binding = "ASSETS"
bucket_name = "tarot-seo-assets"
preview_bucket_name = "tarot-seo-assets-preview"

# 开发环境配置
[env.development]
vars = {
  ENVIRONMENT = "development",
  NEXT_PUBLIC_APP_URL = "http://localhost:3000",
  DEBUG = "true"
}

# 预览环境配置（用于PR预览）
[env.preview]
vars = {
  ENVIRONMENT = "preview",
  NEXT_PUBLIC_APP_URL = "https://tarot-seo-preview.pages.dev",
  DEBUG = "false"
}

# 生产环境配置
[env.production]
vars = {
  ENVIRONMENT = "production",
  NEXT_PUBLIC_APP_URL = "https://your-domain.com",
  DEBUG = "false"
}

# 缓存配置
[site]
bucket = "./public"

# 构建配置
[build]
command = "npm run build"
