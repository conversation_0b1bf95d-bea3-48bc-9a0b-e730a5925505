"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/lib/seo-config.ts":
/*!*******************************!*\
  !*** ./src/lib/seo-config.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateLocalizedMetadata: function() { return /* binding */ generateLocalizedMetadata; },\n/* harmony export */   generateStructuredData: function() { return /* binding */ generateStructuredData; },\n/* harmony export */   pageConfigs: function() { return /* binding */ pageConfigs; },\n/* harmony export */   seoConfigs: function() { return /* binding */ seoConfigs; }\n/* harmony export */ });\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/i18n */ \"(app-pages-browser)/./src/i18n.ts\");\n\n// 基础网站信息\nconst baseUrl = \"http://localhost:3000\" || 0;\nconst siteName = \"Mystical Website\";\n// 多语言SEO配置\nconst seoConfigs = {\n    en: {\n        title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n        description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis. Accurate personality insights and life guidance.\",\n        keywords: [\n            \"mystical tests\",\n            \"free tests\",\n            \"AI analysis\",\n            \"tarot reading\",\n            \"astrology\",\n            \"numerology\",\n            \"personality test\",\n            \"spiritual guidance\",\n            \"fortune telling\",\n            \"horoscope\",\n            \"zodiac signs\",\n            \"tarot cards\",\n            \"crystal healing\",\n            \"palmistry\"\n        ],\n        openGraph: {\n            title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n            description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\",\n            siteName,\n            locale: \"en_US\",\n            type: \"website\"\n        },\n        twitter: {\n            title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n            description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\",\n            card: \"summary_large_image\"\n        },\n        robots: {\n            index: true,\n            follow: true\n        }\n    },\n    \"zh-CN\": {\n        title: \"神秘网站 - 免费塔罗牌、占星术和数字命理测试\",\n        description: \"专业的在线神秘学平台，提供免费的塔罗牌、占星术和数字命理测试，配备AI智能分析。准确的性格洞察和人生指导。\",\n        keywords: [\n            \"神秘学测试\",\n            \"免费测试\",\n            \"AI分析\",\n            \"塔罗牌占卜\",\n            \"占星术\",\n            \"数字命理\",\n            \"性格测试\",\n            \"精神指导\",\n            \"算命\",\n            \"星座运势\",\n            \"十二星座\",\n            \"塔罗牌\",\n            \"水晶疗愈\",\n            \"手相学\",\n            \"梦境解析\"\n        ],\n        openGraph: {\n            title: \"神秘网站 - 免费塔罗牌、占星术和数字命理测试\",\n            description: \"专业的在线神秘学平台，提供免费的塔罗牌、占星术和数字命理测试，配备AI智能分析。\",\n            siteName: \"神秘网站\",\n            locale: \"zh_CN\",\n            type: \"website\"\n        },\n        twitter: {\n            title: \"神秘网站 - 免费塔罗牌、占星术和数字命理测试\",\n            description: \"专业的在线神秘学平台，提供免费的塔罗牌、占星术和数字命理测试，配备AI智能分析。\",\n            card: \"summary_large_image\"\n        },\n        robots: {\n            index: true,\n            follow: true\n        }\n    },\n    \"zh-TW\": {\n        title: \"神秘網站 - 免費塔羅牌、占星術和數字命理測試\",\n        description: \"專業的線上神秘學平台，提供免費的塔羅牌、占星術和數字命理測試，配備AI智慧分析。準確的性格洞察和人生指導。\",\n        keywords: [\n            \"神秘學測試\",\n            \"免費測試\",\n            \"AI分析\",\n            \"塔羅牌占卜\",\n            \"占星術\",\n            \"數字命理\",\n            \"性格測試\",\n            \"精神指導\",\n            \"算命\",\n            \"星座運勢\",\n            \"塔羅牌\",\n            \"水晶療癒\",\n            \"手相學\",\n            \"夢境解析\"\n        ],\n        openGraph: {\n            title: \"神秘網站 - 免費塔羅牌、占星術和數字命理測試\",\n            description: \"專業的線上神秘學平台，提供免費的塔羅牌、占星術和數字命理測試，配備AI智慧分析。\",\n            siteName,\n            locale: \"zh_TW\",\n            type: \"website\"\n        },\n        twitter: {\n            title: \"神秘網站 - 免費塔羅牌、占星術和數字命理測試\",\n            description: \"專業的線上神秘學平台，提供免費的塔羅牌、占星術和數字命理測試，配備AI智慧分析。\",\n            card: \"summary_large_image\"\n        },\n        robots: {\n            index: true,\n            follow: true\n        }\n    },\n    es: {\n        title: \"Sitio Web M\\xedstico - Pruebas Gratuitas de Tarot, Astrolog\\xeda y Numerolog\\xeda\",\n        description: \"Plataforma m\\xedstica online profesional que ofrece pruebas gratuitas de tarot, astrolog\\xeda y numerolog\\xeda con an\\xe1lisis impulsado por IA. Perspectivas precisas de personalidad y orientaci\\xf3n vital.\",\n        keywords: [\n            \"pruebas m\\xedsticas\",\n            \"pruebas gratuitas\",\n            \"an\\xe1lisis IA\",\n            \"lectura de tarot\",\n            \"astrolog\\xeda\",\n            \"numerolog\\xeda\",\n            \"test de personalidad\",\n            \"gu\\xeda espiritual\",\n            \"adivinaci\\xf3n\",\n            \"hor\\xf3scopo\",\n            \"signos del zod\\xedaco\",\n            \"cartas del tarot\",\n            \"sanaci\\xf3n con cristales\",\n            \"quiromancia\"\n        ],\n        openGraph: {\n            title: \"Sitio Web M\\xedstico - Pruebas Gratuitas de Tarot, Astrolog\\xeda y Numerolog\\xeda\",\n            description: \"Plataforma m\\xedstica online profesional que ofrece pruebas gratuitas de tarot, astrolog\\xeda y numerolog\\xeda con an\\xe1lisis impulsado por IA.\",\n            siteName: \"Sitio Web M\\xedstico\",\n            locale: \"es_ES\",\n            type: \"website\"\n        },\n        twitter: {\n            title: \"Sitio Web M\\xedstico - Pruebas Gratuitas de Tarot, Astrolog\\xeda y Numerolog\\xeda\",\n            description: \"Plataforma m\\xedstica online profesional que ofrece pruebas gratuitas de tarot, astrolog\\xeda y numerolog\\xeda con an\\xe1lisis impulsado por IA.\",\n            card: \"summary_large_image\"\n        },\n        robots: {\n            index: true,\n            follow: true\n        }\n    },\n    pt: {\n        title: \"Site M\\xedstico - Testes Gratuitos de Tar\\xf4, Astrologia e Numerologia\",\n        description: \"Plataforma m\\xedstica online profissional oferecendo testes gratuitos de tar\\xf4, astrologia e numerologia com an\\xe1lise alimentada por IA. Insights precisos de personalidade e orienta\\xe7\\xe3o de vida.\",\n        keywords: [\n            \"testes m\\xedsticos\",\n            \"testes gratuitos\",\n            \"an\\xe1lise IA\",\n            \"leitura de tar\\xf4\",\n            \"astrologia\",\n            \"numerologia\",\n            \"teste de personalidade\",\n            \"orienta\\xe7\\xe3o espiritual\",\n            \"adivinha\\xe7\\xe3o\",\n            \"hor\\xf3scopo\",\n            \"signos do zod\\xedaco\",\n            \"cartas de tar\\xf4\",\n            \"cura com cristais\",\n            \"quiromancia\"\n        ],\n        openGraph: {\n            title: \"Site M\\xedstico - Testes Gratuitos de Tar\\xf4, Astrologia e Numerologia\",\n            description: \"Plataforma m\\xedstica online profissional oferecendo testes gratuitos de tar\\xf4, astrologia e numerologia com an\\xe1lise alimentada por IA.\",\n            siteName: \"Site M\\xedstico\",\n            locale: \"pt_BR\",\n            type: \"website\"\n        },\n        twitter: {\n            title: \"Site M\\xedstico - Testes Gratuitos de Tar\\xf4, Astrologia e Numerologia\",\n            description: \"Plataforma m\\xedstica online profissional oferecendo testes gratuitos de tar\\xf4, astrologia e numerologia com an\\xe1lise alimentada por IA.\",\n            card: \"summary_large_image\"\n        },\n        robots: {\n            index: true,\n            follow: true\n        }\n    },\n    hi: {\n        title: \"रहस्यमय वेबसाइट - मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण\",\n        description: \"पेशेवर ऑनलाइन रहस्यमय प्लेटफॉर्म जो AI-संचालित विश्लेषण के साथ मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण प्रदान करता है। सटीक व्यक्तित्व अंतर्दृष्टि और जीवन मार्गदर्शन।\",\n        keywords: [\n            \"रहस्यमय परीक्षण\",\n            \"मुफ्त परीक्षण\",\n            \"AI विश्लेषण\",\n            \"टैरो रीडिंग\",\n            \"ज्योतिष\",\n            \"अंकशास्त्र\",\n            \"व्यक्तित्व परीक्षण\",\n            \"आध्यात्मिक मार्गदर्शन\",\n            \"भविष्यवाणी\",\n            \"राशिफल\",\n            \"राशि चक्र\",\n            \"टैरो कार्ड\",\n            \"क्रिस्टल हीलिंग\",\n            \"हस्तरेखा\"\n        ],\n        openGraph: {\n            title: \"रहस्यमय वेबसाइट - मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण\",\n            description: \"पेशेवर ऑनलाइन रहस्यमय प्लेटफॉर्म जो AI-संचालित विश्लेषण के साथ मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण प्रदान करता है।\",\n            siteName: \"रहस्यमय वेबसाइट\",\n            locale: \"hi_IN\",\n            type: \"website\"\n        },\n        twitter: {\n            title: \"रहस्यमय वेबसाइट - मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण\",\n            description: \"पेशेवर ऑनलाइन रहस्यमय प्लेटफॉर्म जो AI-संचालित विश्लेषण के साथ मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण प्रदान करता है।\",\n            card: \"summary_large_image\"\n        },\n        robots: {\n            index: true,\n            follow: true\n        }\n    },\n    ja: {\n        title: \"ミスティカルウェブサイト - 無料タロット、占星術、数秘術テスト\",\n        description: \"AI分析を搭載した無料のタロット、占星術、数秘術テストを提供するプロフェッショナルなオンライン神秘学プラットフォーム。正確な性格洞察と人生指導。\",\n        keywords: [\n            \"神秘学テスト\",\n            \"無料テスト\",\n            \"AI分析\",\n            \"タロット占い\",\n            \"占星術\",\n            \"数秘術\",\n            \"性格テスト\",\n            \"スピリチュアルガイダンス\",\n            \"占い\",\n            \"ホロスコープ\",\n            \"星座\",\n            \"タロットカード\",\n            \"クリスタルヒーリング\",\n            \"手相占い\"\n        ],\n        openGraph: {\n            title: \"ミスティカルウェブサイト - 無料タロット、占星術、数秘術テスト\",\n            description: \"AI分析を搭載した無料のタロット、占星術、数秘術テストを提供するプロフェッショナルなオンライン神秘学プラットフォーム。\",\n            siteName: \"ミスティカルウェブサイト\",\n            locale: \"ja_JP\",\n            type: \"website\"\n        },\n        twitter: {\n            title: \"ミスティカルウェブサイト - 無料タロット、占星術、数秘術テスト\",\n            description: \"AI分析を搭載した無料のタロット、占星術、数秘術テストを提供するプロフェッショナルなオンライン神秘学プラットフォーム。\",\n            card: \"summary_large_image\"\n        },\n        robots: {\n            index: true,\n            follow: true\n        }\n    }\n};\n// 生成多语言元数据\nfunction generateLocalizedMetadata(locale) {\n    let path = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"\", overrides = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    var _mergedConfig_robots, _mergedConfig_robots1, _mergedConfig_robots2, _mergedConfig_robots3;\n    const config = seoConfigs[locale];\n    const mergedConfig = {\n        ...config,\n        ...overrides\n    };\n    const url = \"\".concat(baseUrl, \"/\").concat(locale).concat(path);\n    // 生成hreflang链接\n    const languages = Object.fromEntries(_i18n__WEBPACK_IMPORTED_MODULE_0__.locales.map((loc)=>[\n            _i18n__WEBPACK_IMPORTED_MODULE_0__.languageConfig[loc].hreflang,\n            \"\".concat(baseUrl, \"/\").concat(loc).concat(path)\n        ]));\n    var _mergedConfig_robots_index, _mergedConfig_robots_follow, _mergedConfig_robots_index1, _mergedConfig_robots_follow1;\n    return {\n        title: mergedConfig.title,\n        description: mergedConfig.description,\n        keywords: mergedConfig.keywords.join(\", \"),\n        authors: [\n            {\n                name: \"Mystical Website Team\"\n            }\n        ],\n        creator: \"Mystical Website\",\n        publisher: \"Mystical Website\",\n        robots: {\n            index: (_mergedConfig_robots_index = (_mergedConfig_robots = mergedConfig.robots) === null || _mergedConfig_robots === void 0 ? void 0 : _mergedConfig_robots.index) !== null && _mergedConfig_robots_index !== void 0 ? _mergedConfig_robots_index : true,\n            follow: (_mergedConfig_robots_follow = (_mergedConfig_robots1 = mergedConfig.robots) === null || _mergedConfig_robots1 === void 0 ? void 0 : _mergedConfig_robots1.follow) !== null && _mergedConfig_robots_follow !== void 0 ? _mergedConfig_robots_follow : true,\n            googleBot: {\n                index: (_mergedConfig_robots_index1 = (_mergedConfig_robots2 = mergedConfig.robots) === null || _mergedConfig_robots2 === void 0 ? void 0 : _mergedConfig_robots2.index) !== null && _mergedConfig_robots_index1 !== void 0 ? _mergedConfig_robots_index1 : true,\n                follow: (_mergedConfig_robots_follow1 = (_mergedConfig_robots3 = mergedConfig.robots) === null || _mergedConfig_robots3 === void 0 ? void 0 : _mergedConfig_robots3.follow) !== null && _mergedConfig_robots_follow1 !== void 0 ? _mergedConfig_robots_follow1 : true,\n                \"max-video-preview\": -1,\n                \"max-image-preview\": \"large\",\n                \"max-snippet\": -1\n            }\n        },\n        openGraph: {\n            type: mergedConfig.openGraph.type,\n            locale: mergedConfig.openGraph.locale,\n            url,\n            siteName: mergedConfig.openGraph.siteName,\n            title: mergedConfig.openGraph.title,\n            description: mergedConfig.openGraph.description,\n            images: [\n                {\n                    url: \"\".concat(baseUrl, \"/images/og-image-\").concat(locale, \".jpg\"),\n                    width: 1200,\n                    height: 630,\n                    alt: mergedConfig.openGraph.title\n                }\n            ]\n        },\n        twitter: {\n            card: mergedConfig.twitter.card,\n            title: mergedConfig.twitter.title,\n            description: mergedConfig.twitter.description,\n            creator: \"@mystical_website\",\n            images: [\n                \"\".concat(baseUrl, \"/images/twitter-image-\").concat(locale, \".jpg\")\n            ]\n        },\n        alternates: {\n            canonical: url,\n            languages\n        },\n        verification: {\n            google: \"your-google-site-verification\" || 0,\n            other: {\n                \"msvalidate.01\": \"your-bing-site-verification\" || 0\n            }\n        },\n        other: {\n            \"content-language\": _i18n__WEBPACK_IMPORTED_MODULE_0__.languageConfig[locale].locale\n        }\n    };\n}\n// 生成结构化数据\nfunction generateStructuredData(locale) {\n    let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"website\";\n    const config = seoConfigs[locale];\n    const url = \"\".concat(baseUrl, \"/\").concat(locale);\n    const baseStructuredData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": type === \"website\" ? \"WebSite\" : type === \"article\" ? \"Article\" : \"Organization\",\n        name: config.openGraph.siteName,\n        url,\n        description: config.description,\n        inLanguage: _i18n__WEBPACK_IMPORTED_MODULE_0__.languageConfig[locale].hreflang\n    };\n    if (type === \"website\") {\n        return {\n            ...baseStructuredData,\n            \"@type\": \"WebSite\",\n            potentialAction: {\n                \"@type\": \"SearchAction\",\n                target: \"\".concat(url, \"/search?q={search_term_string}\"),\n                \"query-input\": \"required name=search_term_string\"\n            },\n            publisher: {\n                \"@type\": \"Organization\",\n                name: \"Mystical Website\",\n                url: baseUrl,\n                logo: \"\".concat(baseUrl, \"/images/logo.png\")\n            }\n        };\n    }\n    if (type === \"organization\") {\n        return {\n            ...baseStructuredData,\n            \"@type\": \"Organization\",\n            logo: \"\".concat(baseUrl, \"/images/logo.png\"),\n            contactPoint: {\n                \"@type\": \"ContactPoint\",\n                contactType: \"customer service\",\n                availableLanguage: _i18n__WEBPACK_IMPORTED_MODULE_0__.locales.map((loc)=>_i18n__WEBPACK_IMPORTED_MODULE_0__.languageConfig[loc].name)\n            },\n            sameAs: [\n                \"https://twitter.com/mystical_website\",\n                \"https://facebook.com/mystical_website\",\n                \"https://instagram.com/mystical_website\"\n            ]\n        };\n    }\n    return baseStructuredData;\n}\n// 页面特定的SEO配置\nconst pageConfigs = {\n    home: (locale)=>({\n            title: seoConfigs[locale].title,\n            description: seoConfigs[locale].description\n        }),\n    tarot: (locale)=>{\n        const titles = {\n            en: \"Free Tarot Reading - AI-Powered Tarot Card Analysis\",\n            \"zh-CN\": \"免费塔罗牌占卜 - AI智能塔罗牌分析\",\n            \"zh-TW\": \"免費塔羅牌占卜 - AI智慧塔羅牌分析\",\n            es: \"Lectura de Tarot Gratuita - An\\xe1lisis de Cartas del Tarot con IA\",\n            pt: \"Leitura de Tar\\xf4 Gratuita - An\\xe1lise de Cartas de Tar\\xf4 com IA\",\n            hi: \"मुफ्त टैरो रीडिंग - AI-संचालित टैरो कार्ड विश्लेषण\",\n            ja: \"無料タロット占い - AI搭載タロットカード分析\"\n        };\n        const descriptions = {\n            en: \"Get accurate tarot card readings with our AI-powered analysis. Discover insights about your past, present, and future through professional tarot interpretation.\",\n            \"zh-CN\": \"通过我们的AI智能分析获得准确的塔罗牌占卜。通过专业的塔罗牌解读发现您过去、现在和未来的洞察。\",\n            \"zh-TW\": \"透過我們的AI智慧分析獲得準確的塔羅牌占卜。透過專業的塔羅牌解讀發現您過去、現在和未來的洞察。\",\n            es: \"Obt\\xe9n lecturas precisas de cartas del tarot con nuestro an\\xe1lisis impulsado por IA. Descubre perspectivas sobre tu pasado, presente y futuro a trav\\xe9s de la interpretaci\\xf3n profesional del tarot.\",\n            pt: \"Obtenha leituras precisas de cartas de tar\\xf4 com nossa an\\xe1lise alimentada por IA. Descubra insights sobre seu passado, presente e futuro atrav\\xe9s da interpreta\\xe7\\xe3o profissional do tar\\xf4.\",\n            hi: \"हमारे AI-संचालित विश्लेषण के साथ सटीक टैरो कार्ड रीडिंग प्राप्त करें। पेशेवर टैरो व्याख्या के माध्यम से अपने अतीत, वर्तमान और भविष्य के बारे में अंतर्दृष्टि खोजें।\",\n            ja: \"AI搭載の分析で正確なタロットカード占いを受けましょう。プロのタロット解釈を通じて、あなたの過去、現在、未来についての洞察を発見してください。\"\n        };\n        return {\n            title: titles[locale],\n            description: descriptions[locale]\n        };\n    },\n    astrology: (locale)=>{\n        const titles = {\n            en: \"Free Astrology Reading - Zodiac Signs & Horoscope Analysis\",\n            \"zh-CN\": \"免费占星术分析 - 星座和星座运势分析\",\n            \"zh-TW\": \"免費占星術分析 - 星座和星座運勢分析\",\n            es: \"Lectura de Astrolog\\xeda Gratuita - An\\xe1lisis de Signos del Zod\\xedaco y Hor\\xf3scopo\",\n            pt: \"Leitura de Astrologia Gratuita - An\\xe1lise de Signos do Zod\\xedaco e Hor\\xf3scopo\",\n            hi: \"मुफ्त ज्योतिष रीडिंग - राशि चक्र और राशिफल विश्लेषण\",\n            ja: \"無料占星術鑑定 - 星座とホロスコープ分析\"\n        };\n        const descriptions = {\n            en: \"Explore your astrological profile with detailed zodiac sign analysis and personalized horoscope readings. Understand your personality traits and cosmic influences.\",\n            \"zh-CN\": \"通过详细的星座分析和个性化星座运势阅读探索您的占星档案。了解您的性格特征和宇宙影响。\",\n            \"zh-TW\": \"透過詳細的星座分析和個性化星座運勢閱讀探索您的占星檔案。了解您的性格特徵和宇宙影響。\",\n            es: \"Explora tu perfil astrol\\xf3gico con an\\xe1lisis detallado de signos del zod\\xedaco y lecturas de hor\\xf3scopo personalizadas. Comprende tus rasgos de personalidad e influencias c\\xf3smicas.\",\n            pt: \"Explore seu perfil astrol\\xf3gico com an\\xe1lise detalhada de signos do zod\\xedaco e leituras de hor\\xf3scopo personalizadas. Compreenda seus tra\\xe7os de personalidade e influ\\xeancias c\\xf3smicas.\",\n            hi: \"विस्तृत राशि चक्र विश्लेषण और व्यक्तिगत राशिफल रीडिंग के साथ अपनी ज्योतिषीय प्रोफ़ाइल का अन्वेषण करें। अपने व्यक्तित्व लक्षणों और ब्रह्मांडीय प्रभावों को समझें।\",\n            ja: \"詳細な星座分析とパーソナライズされたホロスコープ鑑定であなたの占星術プロファイルを探求しましょう。あなたの性格特性と宇宙の影響を理解してください。\"\n        };\n        return {\n            title: titles[locale],\n            description: descriptions[locale]\n        };\n    },\n    numerology: (locale)=>{\n        const titles = {\n            en: \"Free Numerology Reading - Life Path Number Calculator\",\n            \"zh-CN\": \"免费数字命理分析 - 生命路径数字计算器\",\n            \"zh-TW\": \"免費數字命理分析 - 生命路徑數字計算器\",\n            es: \"Lectura de Numerolog\\xeda Gratuita - Calculadora del N\\xfamero del Camino de Vida\",\n            pt: \"Leitura de Numerologia Gratuita - Calculadora do N\\xfamero do Caminho da Vida\",\n            hi: \"मुफ्त अंकशास्त्र रीडिंग - जीवन पथ संख्या कैलकुलेटर\",\n            ja: \"無料数秘術鑑定 - ライフパスナンバー計算機\"\n        };\n        const descriptions = {\n            en: \"Discover your life path number and numerological insights. Calculate your personal numbers and understand their meanings for your life journey.\",\n            \"zh-CN\": \"发现您的生命路径数字和数字命理洞察。计算您的个人数字并了解它们对您人生旅程的意义。\",\n            \"zh-TW\": \"發現您的生命路徑數字和數字命理洞察。計算您的個人數字並了解它們對您人生旅程的意義。\",\n            es: \"Descubre tu n\\xfamero del camino de vida y perspectivas numerol\\xf3gicas. Calcula tus n\\xfameros personales y comprende sus significados para tu viaje de vida.\",\n            pt: \"Descubra seu n\\xfamero do caminho da vida e insights numerol\\xf3gicos. Calcule seus n\\xfameros pessoais e compreenda seus significados para sua jornada de vida.\",\n            hi: \"अपनी जीवन पथ संख्या और अंकशास्त्रीय अंतर्दृष्टि खोजें। अपनी व्यक्तिगत संख्याओं की गणना करें और अपनी जीवन यात्रा के लिए उनके अर्थों को समझें।\",\n            ja: \"あなたのライフパスナンバーと数秘術の洞察を発見しましょう。あなたの個人的な数字を計算し、人生の旅路におけるその意味を理解してください。\"\n        };\n        return {\n            title: titles[locale],\n            description: descriptions[locale]\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/seo-config.ts\n"));

/***/ })

});