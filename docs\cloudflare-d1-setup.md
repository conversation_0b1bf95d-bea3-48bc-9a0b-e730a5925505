# Cloudflare D1数据库设置指南

## 概述

本项目已从PostgreSQL迁移到Cloudflare D1，这是一个基于SQLite的边缘数据库，提供全球分布式存储和低延迟访问。

## D1的优势

### 技术优势
- **边缘分布**：数据存储在全球边缘节点，降低延迟
- **自动扩展**：无需管理服务器，自动处理负载
- **SQLite兼容**：使用熟悉的SQL语法
- **JSON支持**：原生JSON函数，适合复杂数据结构

### 成本优势
- **免费额度**：100K读取/天，50K写入/天
- **按使用付费**：超出免费额度后按实际使用计费
- **无基础设施成本**：无需维护数据库服务器

## 设置步骤

### 1. 安装Wrangler CLI

```bash
npm install -g wrangler
# 或者使用项目本地版本
npm install wrangler --save-dev
```

### 2. 登录Cloudflare

```bash
wrangler login
```

### 3. 创建D1数据库

```bash
# 创建统一数据库（开发和生产环境共用）
wrangler d1 create tarot-seo-db --location=enam

# 或者使用npm脚本
npm run d1:create
```

#### 数据库位置选择说明

我们选择 `enam`（东北美）作为数据库位置，原因如下：

- **全球连接性最佳**：东北美地区是互联网的核心枢纽
- **低延迟覆盖**：对全球用户都有相对较低的延迟
- **稳定性高**：Cloudflare在该地区的基础设施最为成熟
- **多语言友好**：适合服务全球多语言用户群体

**可用位置选项**：
- `enam` - 东北美（推荐）
- `weur` - 西欧（欧洲用户为主时选择）
- `apac` - 亚太（亚洲用户为主时选择）
- `wnam` - 西北美
- `eeur` - 东欧
- `oc` - 大洋洲

### 4. 配置wrangler.toml

将创建数据库时返回的配置信息添加到`wrangler.toml`：

```toml
# 统一数据库配置（开发和生产环境共用）
[[d1_databases]]
binding = "DB"
database_name = "tarot-seo-db"
database_id = "your-database-id-here" # 替换为实际的数据库ID
location = "enam" # 东北美地区

# 开发环境配置
[env.development]
vars = { ENVIRONMENT = "development" }

# 生产环境配置
[env.production]
vars = { ENVIRONMENT = "production" }
```

#### 统一数据库的优势

- **简化管理**：只需维护一个数据库
- **数据一致性**：开发和生产环境数据同步
- **成本节约**：减少数据库实例数量
- **部署简化**：无需管理多个数据库连接

### 5. 生成数据库Schema

```bash
# 生成Prisma客户端
npm run db:generate

# 创建迁移文件
npx prisma migrate dev --name init

# 应用到本地D1数据库
npm run d1:migrate:local

# 应用到远程D1数据库
npm run d1:migrate:remote
```

## 数据库操作

### 本地开发

```bash
# 在本地D1数据库执行SQL
wrangler d1 execute tarot-seo-db --local --command="SELECT * FROM users LIMIT 5"

# 从文件执行SQL
wrangler d1 execute tarot-seo-db --local --file=./schema.sql
```

### 远程数据库

```bash
# 在远程D1数据库执行SQL
wrangler d1 execute tarot-seo-db --remote --command="SELECT COUNT(*) FROM blog_posts"

# 备份数据库
npm run d1:backup
```

## JSON数据处理

D1原生支持JSON函数，我们的数据模型已适配：

### 数组存储
```sql
-- 存储标签数组
INSERT INTO blog_posts (tags) VALUES ('["react", "nextjs", "typescript"]');

-- 查询包含特定标签的文章
SELECT * FROM blog_posts 
WHERE json_extract(tags, '$') LIKE '%"react"%';

-- 获取标签数量
SELECT title, json_array_length(tags) as tag_count 
FROM blog_posts;
```

### 对象存储
```sql
-- 存储元数据对象
INSERT INTO blog_posts (metadata) VALUES ('{"aiGenerated": true, "readingLevel": "intermediate"}');

-- 查询AI生成的文章
SELECT * FROM blog_posts 
WHERE json_extract(metadata, '$.aiGenerated') = true;
```

## 全球读取副本（Read Replication）

D1支持全球读取副本功能，可以进一步优化全球用户的访问性能：

### 启用读取副本

```bash
# 为数据库启用读取副本（Beta功能）
wrangler d1 read-replication enable tarot-seo-db
```

### 读取副本的优势

- **全球分布**：在所有可用地区自动创建只读副本
- **降低延迟**：用户访问最近的副本，显著减少查询延迟
- **自动同步**：主数据库的更改自动同步到所有副本
- **透明使用**：应用代码无需修改，D1自动路由查询

### 适用场景

- **读多写少**：博客文章、测试结果展示等场景
- **全球用户**：多语言网站的全球用户访问
- **SEO优化**：提升页面加载速度，改善SEO排名

## 性能优化

### 索引策略
```sql
-- 为常用查询创建索引
CREATE INDEX idx_blog_posts_locale_category ON blog_posts(locale, category);
CREATE INDEX idx_blog_posts_status_published ON blog_posts(status, publishedAt);
CREATE INDEX idx_test_results_user_type ON test_results(userId, testType);
```

### 查询优化
- 使用`LIMIT`和`OFFSET`进行分页
- 利用JSON函数进行复杂查询
- 批量操作使用D1的batch API

## 部署流程

### 1. 本地测试
```bash
# 启动本地开发服务器
npm run dev

# 测试数据库连接
wrangler d1 execute tarot-seo-db --local --command="SELECT 1"
```

### 2. 部署到Cloudflare Workers
```bash
# 部署应用
wrangler deploy

# 验证部署
curl https://your-worker.your-subdomain.workers.dev/api/health
```

## 监控和维护

### 数据库监控
- 使用Cloudflare Dashboard查看D1使用情况
- 监控查询性能和错误率
- 设置使用量告警

### 备份策略
```bash
# 定期备份
wrangler d1 export tarot-seo-db --output=./backups/backup-$(date +%Y%m%d).sql

# 恢复备份
wrangler d1 execute tarot-seo-db --file=./backups/backup-20241201.sql
```

### Time Travel功能
D1提供30天的Time Travel功能（付费版），可以恢复到任意时间点：

```bash
# 查看可用的恢复点
wrangler d1 time-travel list tarot-seo-db

# 恢复到特定时间点
wrangler d1 time-travel restore tarot-seo-db --timestamp=2024-12-01T10:00:00Z
```

## 故障排除

### 常见问题

1. **连接错误**
   - 检查wrangler.toml配置
   - 确认数据库ID正确
   - 验证Cloudflare账户权限

2. **查询超时**
   - 优化查询语句
   - 添加适当索引
   - 检查查询复杂度

3. **JSON解析错误**
   - 验证JSON格式
   - 使用json_valid()函数检查
   - 处理null值情况

### 调试技巧
```bash
# 启用详细日志
wrangler d1 execute tarot-seo-db --local --command="PRAGMA table_info(blog_posts)"

# 检查索引使用情况
wrangler d1 execute tarot-seo-db --local --command="EXPLAIN QUERY PLAN SELECT * FROM blog_posts WHERE locale = 'en'"
```

## 迁移检查清单

- [ ] 创建D1数据库（统一数据库）
- [ ] 更新wrangler.toml配置
- [ ] 运行数据库迁移
- [ ] 测试JSON数据处理
- [ ] 验证索引创建
- [ ] 更新环境变量
- [ ] 测试本地开发环境
- [ ] 部署到生产环境
- [ ] 启用读取副本（可选）
- [ ] 设置监控和告警
- [ ] 配置备份策略

## 相关资源

- [Cloudflare D1文档](https://developers.cloudflare.com/d1/)
- [Wrangler CLI文档](https://developers.cloudflare.com/workers/wrangler/)
- [SQLite JSON函数](https://www.sqlite.org/json1.html)
- [Prisma D1适配器](https://www.prisma.io/docs/orm/overview/databases/cloudflare-d1)
