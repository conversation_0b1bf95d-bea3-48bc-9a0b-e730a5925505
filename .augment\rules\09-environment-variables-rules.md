---
type: "conditional_apply"
description: "环境变量配置和管理规范"
---

# 环境变量配置和管理规范

## 环境变量文件管理

### 文件层级和用途
```bash
# 环境变量文件层级（按优先级排序）
.env.development    # 开发环境配置，Git跟踪
.env.production     # 生产环境配置，Git跟踪
.env.example        # 配置模板和文档，Git跟踪
```

### 文件职责分工
```typescript
const ENV_FILE_RESPONSIBILITIES = {
  '.env.development': {
    purpose: '开发环境配置',
    gitIgnore: false,
    environment: 'NODE_ENV=development',
    contains: [
      '开发环境默认值',
      '本地服务配置',
      '开发工具设置',
      '调试模式配置',
      '占位符API密钥'
    ],
    example: 'NODE_ENV=development\nDEBUG=true\nQWEN_API_KEY="your-qwen-api-key"'
  },

  '.env.production': {
    purpose: '生产环境配置',
    gitIgnore: false,
    environment: 'NODE_ENV=production',
    contains: [
      '生产环境配置模板',
      '性能优化设置',
      '安全配置',
      '监控配置',
      '占位符API密钥'
    ],
    example: 'NODE_ENV=production\nDEBUG=false\nQWEN_API_KEY="your-production-qwen-api-key"'
  },

  '.env.example': {
    purpose: '配置模板和文档',
    gitIgnore: false,
    contains: [
      '所有环境变量的示例',
      '详细的配置说明',
      '注册链接和获取方式',
      '配置分组和注释'
    ],
    example: '# 通义千问API密钥\n# 注册地址: https://dashscope.aliyun.com/\nQWEN_API_KEY="your-qwen-api-key"'
  }
};
```

## 环境变量命名规范

### 命名约定
```typescript
// 环境变量命名规范
const NAMING_CONVENTIONS = {
  // 1. 使用大写字母和下划线
  correct: 'DATABASE_URL',
  incorrect: 'databaseUrl',

  // 2. 使用描述性前缀
  prefixes: {
    'NEXT_PUBLIC_': '客户端可见变量',
    'DATABASE_': '数据库相关',
    'REDIS_': '缓存相关',
    'SMTP_': '邮件服务',
    'SENTRY_': '错误监控',
    'AI_': 'AI服务通用前缀',
    'QWEN_': '通义千问特定',
    'CLOUDFLARE_': 'Cloudflare服务'
  },

  // 3. 语义化命名
  semantic: {
    'API_KEY': 'API密钥',
    'API_URL': 'API端点',
    'SECRET': '密钥/令牌',
    'TOKEN': '访问令牌',
    'URL': '完整URL地址',
    'HOST': '主机地址',
    'PORT': '端口号',
    'ID': '标识符',
    'NAME': '名称'
  },

  // 4. 避免的命名
  avoid: [
    'KEY', // 太泛化，使用具体的 API_KEY
    'SECRET', // 太泛化，使用具体的 JWT_SECRET
    'URL', // 太泛化，使用具体的 DATABASE_URL
    'CONFIG' // 太泛化，使用具体配置名
  ]
};
```

### 变量分组规范
```bash
# ============================================================================
# 应用基础配置
# ============================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Mystical Website"

# ============================================================================
# 数据库配置
# ============================================================================
DATABASE_URL="file:./dev.db"
DIRECT_URL="file:./dev.db"

# ============================================================================
# 缓存配置
# ============================================================================
REDIS_URL="redis://localhost:6379"
UPSTASH_REDIS_REST_URL="your-upstash-redis-url"
UPSTASH_REDIS_REST_TOKEN="your-upstash-redis-token"

# ============================================================================
# AI服务配置
# ============================================================================
# 通义千问 (主要AI服务)
QWEN_API_KEY="your-qwen-api-key"
QWEN_API_URL="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# 豆包 (备用AI服务)
DOUBAO_API_KEY="your-doubao-api-key"
DOUBAO_API_URL="https://ark.cn-beijing.volces.com/api/v3/chat/completions"
```

## 环境变量安全规范

### 敏感信息分类
```typescript
const SECURITY_CLASSIFICATION = {
  // 高敏感 - 绝不能提交到Git
  critical: [
    'API_KEY', 'SECRET', 'TOKEN', 'PASSWORD',
    'PRIVATE_KEY', 'CERTIFICATE', 'CREDENTIALS'
  ],

  // 中敏感 - 可以有占位符，真实值不提交
  medium: [
    'DATABASE_URL', 'REDIS_URL', 'SMTP_USER',
    'ACCOUNT_ID', 'PROJECT_ID'
  ],

  // 低敏感 - 可以提交真实值
  low: [
    'APP_NAME', 'APP_URL', 'ENVIRONMENT',
    'LOCALE', 'TIMEZONE', 'DEBUG'
  ],

  // 公开 - NEXT_PUBLIC_前缀，客户端可见
  public: [
    'NEXT_PUBLIC_APP_URL', 'NEXT_PUBLIC_APP_NAME',
    'NEXT_PUBLIC_UMAMI_WEBSITE_ID', 'NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION'
  ]
};
```

### 安全最佳实践
```typescript
const SECURITY_BEST_PRACTICES = {
  // 1. 文件权限管理
  filePermissions: {
    '.env.local': '600 (仅所有者可读写)',
    '.env': '644 (所有者可读写，其他人只读)',
    '.env.example': '644 (所有者可读写，其他人只读)'
  },

  // 2. Git管理
  gitManagement: {
    ignore: ['.env.local', '.env.*.local'],
    track: ['.env', '.env.example'],
    never: ['真实的API密钥', '生产环境密码']
  },

  // 3. 密钥轮换
  keyRotation: {
    frequency: '每90天轮换一次API密钥',
    emergency: '发现泄露时立即轮换',
    automation: '使用CI/CD自动化轮换流程'
  },

  // 4. 验证规则
  validation: {
    required: '生产环境必须验证所有必需变量',
    format: '验证URL、邮箱等格式',
    strength: '密钥长度至少32字符',
    https: '生产环境URL必须使用HTTPS'
  }
};
```

## 环境变量文档规范

### 注释和文档格式
```bash
# ============================================================================
# 分组标题 - 使用等号线分隔
# ============================================================================

# 单行注释 - 解释变量用途
VARIABLE_NAME="value"

# 多行注释 - 详细说明
# 变量说明：这个变量用于...
# 获取方式：在 https://example.com 注册获取
# 注意事项：生产环境必须使用真实值
COMPLEX_VARIABLE="placeholder-value"

# 条件配置 - 说明何时需要
# 可选配置，仅在使用某功能时需要
OPTIONAL_VARIABLE="optional-value"

# 环境特定 - 说明不同环境的差异
# 开发环境: http://localhost:3000
# 生产环境: https://your-domain.com
ENVIRONMENT_SPECIFIC_URL="http://localhost:3000"
```

### .env.example模板规范
```bash
# ============================================================================
# 项目名称 - 环境变量配置模板
# 技术栈说明和主要依赖
#
# 使用说明：
# 1. 复制此文件为 .env.local
# 2. 填入真实的API密钥和配置信息
# 3. 生产环境在部署平台配置，不在代码中
# ============================================================================

# 应用基础配置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Your App Name"

# ============================================================================
# 数据库配置
# ============================================================================
# 本地开发数据库连接
DATABASE_URL="your-database-connection-string"

# ============================================================================
# AI服务配置
# ============================================================================
# 通义千问 API
# 注册地址: https://dashscope.aliyun.com/
# 获取方式: 控制台 -> API-KEY管理
QWEN_API_KEY="your-qwen-api-key"
QWEN_API_URL="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# ============================================================================
# 可选配置（根据需要启用）
# ============================================================================
# 错误监控 - Sentry
# 注册地址: https://sentry.io/
SENTRY_DSN="your-sentry-dsn"

# 网站分析 - Umami
# 部署地址: https://umami.is/
NEXT_PUBLIC_UMAMI_WEBSITE_ID="your-umami-website-id"
```

## 环境变量验证

### 运行时验证
```typescript
// 环境变量验证函数示例
const validateEnvironmentVariables = () => {
  const required = [
    'NEXT_PUBLIC_APP_URL',
    'DATABASE_URL',
    'NEXTAUTH_SECRET'
  ];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // 格式验证
  if (process.env.NEXT_PUBLIC_APP_URL && !process.env.NEXT_PUBLIC_APP_URL.startsWith('http')) {
    throw new Error('NEXT_PUBLIC_APP_URL must be a valid URL');
  }

  // 安全验证
  if (process.env.NODE_ENV === 'production' && process.env.NEXTAUTH_SECRET?.length < 32) {
    throw new Error('NEXTAUTH_SECRET must be at least 32 characters in production');
  }
};
```

### 类型定义
```typescript
// 环境变量类型定义
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      NEXT_PUBLIC_APP_URL: string;
      NEXT_PUBLIC_APP_NAME: string;
      DATABASE_URL: string;
      NEXTAUTH_SECRET: string;
      QWEN_API_KEY?: string;
      REDIS_URL?: string;
      SENTRY_DSN?: string;
    }
  }
}
```

## 环境变量最佳实践总结

### 开发工作流
1. **开发环境**: 直接在 `.env.development` 中配置真实的API密钥
2. **生产环境**: 在 `.env.production` 中配置生产环境的API密钥
3. **部署覆盖**: 部署平台的环境变量会自动覆盖文件中的配置
4. **文档维护**: 保持 `.env.example` 与实际配置同步

### 部署流程
1. **开发环境**: Next.js自动加载 `.env.development`
2. **生产环境**: Next.js自动加载 `.env.production`
3. **部署平台覆盖**: Vercel/Cloudflare环境变量覆盖文件配置
4. **安全保障**: 敏感信息通过部署平台管理

## 环境区分策略

### 最终方案：环境文件分离
```typescript
const ENVIRONMENT_FILE_STRATEGY = {
  // 文件职责
  files: {
    '.env.development': '开发环境配置 + 开发API密钥',
    '.env.production': '生产环境配置 + 生产API密钥',
    '.env.example': '模板文档 + 配置说明'
  },

  // 自动环境检测
  environment_detection: {
    development: {
      trigger: 'NODE_ENV=development 或 npm run dev',
      file: '.env.development 自动加载',
      url: 'http://localhost:3000',
      database: 'wrangler d1 --local',
      debug: true
    },

    production: {
      trigger: 'NODE_ENV=production 或部署构建',
      file: '.env.production 自动加载',
      url: 'https://your-domain.com',
      database: 'Cloudflare D1远程',
      debug: false
    }
  },

  // 部署平台覆盖
  deployment_override: {
    vercel: 'Dashboard环境变量覆盖文件配置',
    cloudflare: 'Pages环境变量覆盖文件配置',
    security: '敏感信息通过平台管理，不在代码中'
  },

  // 优势
  advantages: [
    '完全自动化，无需手动配置',
    '环境隔离，配置清晰',
    'Git跟踪，团队协作友好',
    '部署平台安全覆盖',
    '符合Next.js最佳实践'
  ]
};
```

### 何时需要独立的环境文件
```typescript
const WHEN_TO_USE_SEPARATE_FILES = {
  // 需要独立文件的情况
  use_separate_when: [
    '大型团队开发',
    '复杂的微服务架构',
    '多个数据库实例',
    '复杂的第三方服务集成',
    '严格的企业安全要求'
  ],

  // 当前项目不需要的原因
  current_project_reasons: [
    'Cloudflare D1单一数据库',
    '边缘计算自动处理环境',
    '小团队开发',
    '部署平台提供环境管理'
  ]
};
```

### 安全检查清单
- [ ] 敏感信息不在Git中
- [ ] 生产环境使用强密码
- [ ] 定期轮换API密钥
- [ ] 验证所有必需变量
- [ ] 使用HTTPS URL
- [ ] 文件权限正确设置