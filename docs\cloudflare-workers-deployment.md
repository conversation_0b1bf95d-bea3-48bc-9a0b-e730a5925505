# Cloudflare Workers + D1 部署指南

## 🎯 架构概述

项目已完全迁移到Cloudflare边缘计算架构：

- **Cloudflare Workers**: 边缘计算运行时
- **Cloudflare D1**: SQLite边缘数据库
- **Cloudflare KV**: 键值存储（缓存）
- **Cloudflare R2**: 对象存储（文件）
- **Cloudflare Images**: 图片优化（可选）

## 🚀 快速部署

### 1. 环境准备

```bash
# 安装依赖
npm install

# 登录Cloudflare
wrangler login
```

### 2. 创建Cloudflare资源

```bash
# 创建D1数据库（已完成）
npm run d1:create

# 创建KV命名空间
npm run kv:create
npm run kv:create:preview

# 创建R2存储桶
npm run r2:create
```

### 3. 配置wrangler.toml

更新`wrangler.toml`中的资源ID：

```toml
# 更新KV命名空间ID
[[kv_namespaces]]
binding = "CACHE"
id = "your-actual-kv-namespace-id"
preview_id = "your-actual-preview-kv-namespace-id"

# R2存储桶已配置
[[r2_buckets]]
binding = "ASSETS"
bucket_name = "tarot-seo-assets"
```

### 4. 设置环境变量

在Cloudflare Dashboard中设置生产环境变量：

```bash
# 设置AI服务密钥
wrangler secret put QWEN_API_KEY
wrangler secret put DOUBAO_API_KEY
wrangler secret put ZHIPU_API_KEY

# 设置监控配置
wrangler secret put SENTRY_DSN
wrangler secret put NEXT_PUBLIC_UMAMI_WEBSITE_ID

# 设置邮件服务
wrangler secret put SMTP_HOST
wrangler secret put SMTP_USER
wrangler secret put SMTP_PASS
```

### 5. 数据库迁移

```bash
# 生成Prisma客户端
npm run db:generate

# 本地测试迁移
npm run d1:migrate:local

# 应用到远程数据库
npm run d1:migrate:remote
```

### 6. 部署应用

```bash
# 测试部署（不实际部署）
npm run build:worker

# 正式部署
npm run deploy
```

## 🔧 开发工作流

### 本地开发

```bash
# 启动Next.js开发服务器（前端）
npm run dev

# 启动Cloudflare Workers开发服务器（API）
npm run dev:worker
```

### 混合开发模式

1. **前端开发**: 使用Next.js开发服务器
2. **API开发**: 使用Cloudflare Workers本地环境
3. **数据库**: 本地D1数据库

### 环境配置

- **本地开发**: `.env.local` + 本地D1数据库
- **Workers开发**: `wrangler.toml` + 本地D1数据库
- **生产环境**: Cloudflare Dashboard + 远程D1数据库

## 📊 环境变量管理

### 本地开发 (.env.local)

```bash
# 应用配置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Mystical Website"

# 数据库配置
DATABASE_URL="file:./dev.db"

# AI服务配置
QWEN_API_KEY="your-qwen-api-key"
DOUBAO_API_KEY="your-doubao-api-key"
ZHIPU_API_KEY="your-zhipu-api-key"
```

### Workers环境 (wrangler.toml)

```toml
[vars]
ENVIRONMENT = "development"
NEXT_PUBLIC_APP_NAME = "Mystical Website"
NEXT_PUBLIC_APP_URL = "http://localhost:3000"

[env.production]
vars = { 
  ENVIRONMENT = "production",
  NEXT_PUBLIC_APP_URL = "https://your-domain.com"
}
```

### 生产环境密钥

使用`wrangler secret`管理敏感信息：

```bash
# AI服务
wrangler secret put QWEN_API_KEY
wrangler secret put DOUBAO_API_KEY
wrangler secret put ZHIPU_API_KEY

# 监控服务
wrangler secret put SENTRY_DSN
wrangler secret put SENTRY_AUTH_TOKEN

# 邮件服务
wrangler secret put SMTP_HOST
wrangler secret put SMTP_USER
wrangler secret put SMTP_PASS

# Cloudflare服务
wrangler secret put CLOUDFLARE_R2_ACCESS_KEY_ID
wrangler secret put CLOUDFLARE_R2_SECRET_ACCESS_KEY
```

## 🌐 域名配置

### 1. 添加自定义域名

```bash
# 添加路由
wrangler route add "your-domain.com/*" tarot-seo

# 或在Cloudflare Dashboard中配置
```

### 2. 更新环境变量

```toml
[env.production]
vars = { 
  ENVIRONMENT = "production",
  NEXT_PUBLIC_APP_URL = "https://your-domain.com"
}
```

## 📈 监控和日志

### 1. 实时日志

```bash
# 查看实时日志
wrangler tail

# 查看特定环境的日志
wrangler tail --env production
```

### 2. 性能监控

- **Cloudflare Analytics**: 自动启用
- **Sentry**: 错误监控和性能追踪
- **Umami**: 用户行为分析

### 3. 数据库监控

```bash
# 查看数据库使用情况
wrangler d1 info tarot-seo-db

# 查看查询性能
wrangler d1 execute tarot-seo-db --command="EXPLAIN QUERY PLAN SELECT * FROM blog_posts"
```

## 🔒 安全配置

### 1. API安全

- CORS配置已在Workers中实现
- 速率限制可通过Cloudflare配置
- API密钥通过`wrangler secret`管理

### 2. 数据库安全

- D1数据库自动加密
- 访问控制通过Workers绑定
- 备份策略自动执行

### 3. 文件存储安全

- R2存储桶访问控制
- 图片优化和压缩
- CDN缓存策略

## 🚨 故障排除

### 常见问题

1. **部署失败**
   ```bash
   # 检查配置
   wrangler whoami
   wrangler kv:namespace list
   wrangler d1 list
   ```

2. **数据库连接错误**
   ```bash
   # 测试数据库连接
   wrangler d1 execute tarot-seo-db --command="SELECT 1"
   ```

3. **环境变量问题**
   ```bash
   # 查看环境变量
   wrangler secret list
   ```

### 调试技巧

```bash
# 本地调试
npm run dev:worker

# 查看详细日志
wrangler tail --format=pretty

# 测试API端点
curl https://your-worker.your-subdomain.workers.dev/health
```

## 📋 部署检查清单

- [ ] 创建所有Cloudflare资源（D1、KV、R2）
- [ ] 更新wrangler.toml配置
- [ ] 设置所有环境变量和密钥
- [ ] 运行数据库迁移
- [ ] 测试本地开发环境
- [ ] 执行测试部署
- [ ] 配置自定义域名
- [ ] 设置监控和告警
- [ ] 验证所有API端点
- [ ] 测试文件上传和存储
- [ ] 配置备份策略

## 🔄 CI/CD集成

### GitHub Actions示例

```yaml
name: Deploy to Cloudflare Workers

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run database migrations
        run: npm run d1:migrate:remote
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
      
      - name: Deploy to Cloudflare Workers
        run: npm run deploy
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
```

这个配置提供了完整的Cloudflare Workers + D1架构部署方案，确保项目能够充分利用边缘计算的优势！
